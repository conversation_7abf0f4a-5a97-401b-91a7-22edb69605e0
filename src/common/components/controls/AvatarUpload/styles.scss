.editingContainer {
  cursor: pointer;
  border: 1px dashed #aaa;
  padding: 15px;
}

.photoPreview {
  height: 400px;
}

.undoBtn {
  color: #949494;

  &:hover {
    color: #949494;
  }
}

.smallBtn {
  font-size: 11px;
  font-weight: 400;
}

.IconPosition {
  margin-top: 10px;
}

.modalWrapper {
  :global {
    .swal2-actions {
      justify-content: right !important;
    }

    .swal2-html-container {
      font-size: 16px;
      position: relative;
      float: none;
      line-height: normal;
      vertical-align: top;
      text-align: left;
      display: inline-block !important;
      margin: 0;
      color: #333;
      font-weight: 400;
      max-width: calc(100% - 20px);
      overflow-wrap: break-word;
      box-sizing: border-box;
      justify-content: right !important;
    }

    .swal2-content {
      padding: 0 !important;
    }

    .swal2-icon.swal2-warning {
      border-color: #f8bb86;
      color: #f8bb86;
      width: 80px;
      height: 80px;
      border-width: 4px;
      border-style: solid;
      border-radius: 50%;
      padding: 0;
      position: relative;
      box-sizing: content-box;
      margin: 20px auto;
    }

    // .swal2-icon-content {
    //   font-size: 7.75em;
    //   font-weight: 300
    // }
  }
}
