import classNames from 'classnames';
import React, { FC } from 'react';
import { TIcon } from '../../propTypes';
import IconButton from '../controls/IconButton';
import styles from './ImageRounded.scss';

const ImageRounded: FC<IImageRoundedProps> = ({
  fileName,
  className,
  onClick,
  size = 'form',
  src,
  iconName,
  hoverIconName,
  isDisable,
}) => {
  if (src) {
    return (
      <img
        alt={fileName}
        className={classNames(className, styles[size], {
          [styles.click]: onClick,
        })}
        src={src}
        onClick={onClick}
      />
    );
  }

  if (iconName) {
    return (
      <IconButton
        hasBackground={false}
        hoverIconName={hoverIconName}
        iconClassName={classNames(className, styles.icon, styles[size])}
        iconName={iconName}
        isDisable={isDisable}
        onClick={onClick}
      />
    );
  }

  return null;
};

export default ImageRounded;

interface IImageRoundedProps {
  className?: string;
  fileName?: string;
  onClick?: () => void;
  size?: 'card' | 'form';
  src?: string;
  iconName?: TIcon;
  hoverIconName?: TIcon;
  isDisable?: boolean;
}
