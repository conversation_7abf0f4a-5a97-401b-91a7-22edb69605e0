import React, { useCallback, useMemo } from 'react';
import { useQuery } from 'react-apollo';
import { get } from 'lodash';

import Breadcrumbs from '../../../../../common/components/utils/Breadcrumbs';
import eContentItem from '../../../../../common/data/eContent/eContentItem.graphql';
import eContentItemContent from '../../../../../common/data/eContent/eContentItemContent.graphql';
import { cookEContentClobs } from '../form/tabs/EContentItemContentsTab/form/EContentItemContentForm';
import IEContentItem from '../../../../../common/abstract/EContent/IEContentItem';
import IEContentContent from '../../../../../common/abstract/EContent/IEContentContent';
import IEContentResource from '../../../../../common/abstract/EContent/IEContentResource';

interface EContentItemContentBreadcrumbsProps {
  libraryId: string;
  itemId: string;
  contentId: string;
  children: React.ReactNode;
}

const EContentItemContentBreadcrumbs: React.FC<EContentItemContentBreadcrumbsProps> = ({
  libraryId,
  itemId,
  contentId,
  children,
}) => {
  const isNew = contentId === 'new';

  // Query for item data (includes resource)
  const { data: itemData } = useQuery(eContentItem, {
    variables: { id: parseInt(itemId, 10) },
  });

  // Query for content data
  const { data: contentData } = useQuery(eContentItemContent, {
    variables: { id: parseInt(contentId, 10) },
    skip: isNew,
  });

  const item = useMemo(
    () => get(itemData, 'eContentItem', {}) as IEContentItem,
    [itemData],
  );

  const resource = useMemo(() => item?.resource || ({} as IEContentResource), [
    item,
  ]);

  const content = useMemo(
    () => get(contentData, 'eContentItemContent', {}) as IEContentContent,
    [contentData],
  );

  const contentHeading = useMemo(() => {
    if (isNew) {
      return 'New Content';
    }
    if (content?.clobs && resource?.attributes) {
      const cookedClobs = cookEContentClobs(content.clobs, resource.attributes);
      return cookedClobs?.heading?.content || 'Content';
    }
    return 'Content';
  }, [content, resource, isNew]);

  // Build breadcrumb anchors
  const anchors = useMemo(() => {
    const crumbs: { route: string; title: string }[] = [];

    // Resource Name
    if (resource?.name) {
      crumbs.push({
        route: `/e-content/libraries/edit/${libraryId}/edit`,
        title: resource.name,
      });
    }

    // Item Name
    if (item?.name) {
      crumbs.push({
        route: `/e-content/libraries/edit/${libraryId}/edit/${itemId}`,
        title: item.name,
      });
    }

    // Content Heading Name
    crumbs.push({
      route: `/e-content/libraries/edit/${libraryId}/edit/${itemId}/contents-list/edit/${contentId}`,
      title: contentHeading,
    });

    // Details (final breadcrumb)
    crumbs.push({
      route: `/e-content/libraries/edit/${libraryId}/edit/${itemId}/contents-list/edit/${contentId}`,
      title: 'Details',
    });

    return crumbs;
  }, [resource, item, contentHeading, libraryId, itemId, contentId]);

  const getRoute = useCallback((anchor: any) => anchor.route, []);
  const getTitle = useCallback((anchor: any) => anchor.title, []);

  return (
    <Breadcrumbs.Anchors
      anchors={anchors}
      getRoute={getRoute}
      getTitle={getTitle}
    >
      {children}
    </Breadcrumbs.Anchors>
  );
};

export default EContentItemContentBreadcrumbs;
