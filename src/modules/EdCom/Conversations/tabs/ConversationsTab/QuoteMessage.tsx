import React, { useMemo } from 'react';
import classNames from 'classnames';
import IconButton from '../../../../../common/components/controls/IconButton';
import useT from '../../../../../common/components/utils/Translations/useT';
import toEdanaTimestamp9 from '../../../../../common/utils/edanaTimestamp9';
import { IThreadMessageItemQuote } from './abstract/IThreadMessageItem';
import styles from './QuoteMessage.scss';
import stripHtml from '../../../../../common/utils/stripHtml';

const QUOTE_SIZE_LIMIT = 500;
interface IQuoteMessage {
  message?: IThreadMessageItemQuote;
  onCancel?: () => void;
  onClick?: () => void;
}

export default function QuoteMessage({
  message,
  onCancel,
  onClick,
}: IQuoteMessage) {
  const markup = useMemo(() => {
    if (!message?.content) return { __html: '' };

    const wrapper = document.createElement('div');
    wrapper.innerHTML = message.content;

    Array.from(wrapper.querySelectorAll('p')).forEach(p => {
      const inner = p.innerHTML.replace(/\s+/g, '').toLowerCase();
      if (inner === '<br>' || inner === '<br/>') {
        p.remove();
      }
    });

    const elementNodes = Array.from(wrapper.childNodes).filter(
      node =>
        node.nodeType === Node.ELEMENT_NODE &&
        (node as HTMLElement).innerHTML.trim().length > 0,
    ) as HTMLElement[];

    const firstEl = elementNodes[0];
    const lastEl = elementNodes[elementNodes.length - 1];

    if (firstEl && lastEl && firstEl === lastEl) {
      firstEl.innerHTML = `"${firstEl.innerHTML}"`;
    } else {
      if (firstEl) {
        firstEl.innerHTML = `"${firstEl.innerHTML}`;
      }
      if (lastEl) {
        lastEl.innerHTML = `${lastEl.innerHTML}"`;
      }
    }

    const trimmed = stripHtml(wrapper.innerHTML).trim();

    const sliced =
      trimmed.length > QUOTE_SIZE_LIMIT
        ? `${trimmed.slice(0, QUOTE_SIZE_LIMIT)}..."`
        : trimmed;

    return { __html: sliced };
  }, [message]);

  const t = useT();
  if (!message) {
    return <></>;
  }

  return (
    <div
      className={classNames(styles.container, 'mt-10', 'p-5', 'bg-btn', {
        [styles.clickable]: onClick,
      })}
      onClick={onClick}
    >
      <div className="pl-5 w-100 border-left-lg border-grey">
        {/*eslint-disable-next-line react/no-danger*/}
        <div dangerouslySetInnerHTML={markup} />
        <span>
          {`${message.person.fullName}, ${toEdanaTimestamp9(
            t,
            message.createdAt,
          )}`}
        </span>
        <br />
      </div>
      {onCancel && (
        <IconButton
          isSmall
          className={styles.removeIcon}
          hasBackground={false}
          iconName="cross"
          onClick={onCancel}
        />
      )}
    </div>
  );
}
