/* eslint-disable react/no-multi-comp */
import React, {
  useMemo,
  useRef,
  useCallback,
  forwardRef,
  useState,
  useEffect,
} from 'react';
import classNames from 'classnames';
import { isEmpty } from 'lodash';
import uuidv4 from 'uuid/v4';
import AttachmentField from '../../../../../common/components/containers/EntityForm/fields/AttachmentField';
import { ED_COM_CONVERSATION_ATTACHMENTS } from '../../../../../fsCategories';
import EntityFormFieldSet from '../../../../../common/components/containers/EntityForm/EntityFormFieldSet';
import QuoteMessageField from './QuoteMessageField';
import useStorageSettings from '../../../../../common/components/controls/base/TextEditor/useStorageSettings';
import useCurrentUser from '../../../../../common/data/hooks/useCurrentUser';
import { ICustomButton } from '../../../../../common/components/controls/base/TextEditor/useCustomButtons';
import { TextEditorRef } from '../../../../../common/components/controls/base/TextEditor/TextEditor';
import styles from './ThreadMessageFormBody.scss';
import TextEditorField from '../../../../../common/components/containers/EntityForm/fields/TextEditorField';
import useEntityFormContext from '../../../../../common/components/containers/EntityForm/internal/useEntityFormContext';

const INTERVAL = 100;
const ENTER_KEY = 13;

interface ICustomCounterProps {
  editorRef: React.RefObject<TextEditorRef> | React.ForwardedRef<TextEditorRef>;
}

const CustomCounter: React.FC<ICustomCounterProps> = ({ editorRef }) => {
  const [counterInfo, setCounterInfo] = useState<{
    counter: number;
    counterTitle: string | undefined;
    isFocus: boolean;
  }>({
    counter: 0,
    counterTitle: undefined,
    isFocus: false,
  });

  useEffect(() => {
    const updateCounter = () => {
      const currentRef =
        editorRef && typeof editorRef === 'object' && 'current' in editorRef
          ? editorRef.current
          : null;

      if (currentRef?.getCounterInfo) {
        const info = currentRef.getCounterInfo();
        setCounterInfo(info);
      }
    };

    updateCounter();

    const interval = setInterval(updateCounter, INTERVAL);

    return () => clearInterval(interval);
  }, [editorRef]);

  if (!counterInfo.counterTitle || !counterInfo.isFocus) {
    return null;
  }

  return (
    <span className={styles.customCounter}>{counterInfo.counterTitle}</span>
  );
};

interface IThreadMessageFromBody {
  className?: string;
  isEditMode?: boolean;
  setIsEditMode?: React.Dispatch<React.SetStateAction<boolean>>;
  editingMessageId?: number | null;
  setEditingMessageId?: React.Dispatch<React.SetStateAction<number | null>>;
  parentId?: number | null;
  setIsTextBoxOpen?: React.Dispatch<React.SetStateAction<boolean>>;
}

const ThreadMessageFormBody = forwardRef<TextEditorRef, IThreadMessageFromBody>(
  (
    {
      className,
      isEditMode,
      setIsEditMode,
      editingMessageId,
      setEditingMessageId,
      parentId,
      setIsTextBoxOpen,
    },
    ref,
  ) => {
    const {
      submitForm,
      touched,
      isSubmitting,
      dirty,
      setValues,
      values,
    } = useEntityFormContext();
    const {
      me: { organisationGroupId, tenantId },
    } = useCurrentUser();

    const componentId = useMemo(() => uuidv4(), []);

    const { maxSize } = useStorageSettings(
      ED_COM_CONVERSATION_ATTACHMENTS,
      tenantId,
      organisationGroupId,
    );

    useEffect(() => {
      const handleKeyDown = (event: Event) => {
        const keyboardEvent = event as KeyboardEvent;
        if (
          keyboardEvent.keyCode === ENTER_KEY &&
          !keyboardEvent.shiftKey &&
          !keyboardEvent.ctrlKey &&
          !keyboardEvent.altKey
        ) {
          event.preventDefault();
          event.stopPropagation();

          const content = values?.content || '';
          const attachments = values?.attachments || [];

          if (
            (!isEmpty(content.trim()) || !isEmpty(attachments)) &&
            !isEmpty(touched) &&
            dirty &&
            !isSubmitting
          ) {
            submitForm();
          }

          return false;
        }
        return true;
      };

      const timer = setTimeout(() => {
        const editorElement = document.querySelector(
          `[data-component-id="${componentId}"] .fr-element`,
        );
        if (editorElement) {
          editorElement.addEventListener('keydown', handleKeyDown, true);
        }
      }, INTERVAL);

      return () => {
        clearTimeout(timer);
        const editorElement = document.querySelector(
          `[data-component-id="${componentId}"] .fr-element`,
        );
        if (editorElement) {
          editorElement.removeEventListener('keydown', handleKeyDown, true);
        }
      };
    }, [componentId, submitForm, touched, dirty, isSubmitting, values]);

    const hiddenAttachButtonRef = useRef<HTMLButtonElement>(null);

    const HiddenAttachmentComponent = useCallback(
      ({ onModalOpen }: { onModalOpen: () => void }) => (
        <button
          ref={hiddenAttachButtonRef}
          className="hidden"
          type="button"
          onClick={onModalOpen}
        />
      ),
      [],
    );

    const currentMessageId = values?.id;

    const customButtons: ICustomButton[] = useMemo(() => {
      const actions = [
        {
          id: `fileAttachment-${componentId}`,
          title: 'Attach File',
          icon: 'file-plus',
          onClick: () => {
            if (hiddenAttachButtonRef.current) {
              hiddenAttachButtonRef.current.click();
            }
          },
        },
      ];

      const isThisEditorInEditMode = parentId
        ? editingMessageId === currentMessageId
        : isEditMode;

      if (!isThisEditorInEditMode) {
        actions.push({
          id: `send-${componentId}`,
          title: 'Send',
          icon: 'arrow-right14',
          onClick: () => {
            if (!isEmpty(touched) && !isSubmitting && dirty) {
              submitForm();
            }
          },
        });
      }

      if (isThisEditorInEditMode) {
        actions.push({
          id: `cancel-${componentId}`,
          title: 'Cancel',
          icon: 'cross2',
          onClick: () => {
            setValues({ content: '' });
            if (parentId) {
              setEditingMessageId && setEditingMessageId(null);
              setIsTextBoxOpen && setIsTextBoxOpen(false);
            } else {
              setIsEditMode && setIsEditMode(false);
            }
          },
        });
        actions.push({
          id: 'edit',
          title: 'Edit',
          icon: 'checkmark3',
          onClick: () => {
            if (!isEmpty(touched) && !isSubmitting && dirty) {
              submitForm();
              if (parentId) {
                setEditingMessageId && setEditingMessageId(null);
                setIsTextBoxOpen && setIsTextBoxOpen(false);
              } else {
                setIsEditMode && setIsEditMode(false);
              }
            }
          },
        });
      }

      return actions;
    }, [
      isSubmitting,
      touched,
      dirty,
      submitForm,
      isEditMode,
      setIsEditMode,
      setValues,
      parentId,
      editingMessageId,
      setEditingMessageId,
      currentMessageId,
      setIsTextBoxOpen,
      componentId,
    ]);

    return (
      <EntityFormFieldSet className={className}>
        <QuoteMessageField />
        <div className={styles.showScrollbars} data-component-id={componentId}>
          <TextEditorField
            ref={ref}
            hideCounter
            noLabel
            columns={1}
            config={{ heightMin: 200, heightMax: 200, pastePlain: true }}
            customButtons={customButtons}
            name="content"
            rows={4}
          />
        </div>
        <div
          className={classNames('pl-10 pr-10', {
            hidden: isSubmitting,
          })}
        >
          <AttachmentField
            isDownloadable
            isThreadAttachment
            showAttachmentNameOnly
            addAttachmentComponent={HiddenAttachmentComponent}
            categoryKey={ED_COM_CONVERSATION_ATTACHMENTS}
            columns={1}
            hasLowStrike={false}
            isListHidden={false}
            maxSize={maxSize}
            name="attachments"
            wrapperClassNames={{
              1: classNames('col-lg-12', styles.fieldWrapperClass),
            }}
          />
        </div>
        <CustomCounter editorRef={ref} />
      </EntityFormFieldSet>
    );
  },
);

ThreadMessageFormBody.displayName = 'ThreadMessageFormBody';

export default ThreadMessageFormBody;
