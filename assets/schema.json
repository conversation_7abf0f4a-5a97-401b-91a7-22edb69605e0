{"COMMON": {"Please select an item": "Please select an item", "View Mode": "View Mode", "Edit Mode": "Edit Mode", "This module is under construction. Please check back soon!": "This module is under construction. Please check back soon!", "This #{type} is under construction. Please check back soon!": "This #{type} is under construction. Please check back soon!", "Create": "Create", "Update": "Update", "Autofill": "Autofill", "The changes have not been saved yet, you need to click on the #{button} button in order to save changes": "The changes have not been saved yet, you need to click on the #{button} button in order to save changes", "Proceed": "Proceed", "Remain on page": "<PERSON><PERSON><PERSON> on page", "Remove": "Remove", "Clear Fields": "Clear Fields", "CLEAR": "CLEAR", "Address Line 1": "Address Line 1", "Address Line 2": "Address Line 2", "Address Line 3": "Address Line 3", "Country": "Country", "State": "State", "City": "City", "Postcode": "Postcode", "Status": "Status", "Loading types...": "Loading types...", "Address Type": "Address Type", "Same Address as Other People": "Same Address as Other People", "Loading relations...": "Loading relations...", "This is the address for": "This is the address for", "#{count} Person#{s}": "#{count} Person#{s}", "Are you sure you want to delete this address for #{firstName} #{lastName} ?": "Are you sure you want to delete this address for #{firstName} #{lastName} ?", "Cancel": "Cancel", "Yes": "Yes", "If #{firstName} #{lastName} is no longer at this address, ": "If #{firstName} #{lastName} is no longer at this address, ", "Click here": "Click here", "Add": "Add", "Save": "Save", "Close": "Close", "Apply": "Apply", "Assign #{title}": "Assign #{title}", "Add Document": "Add Document", "Error in loading pdf file.": "Error in loading pdf file.", "Parent Level": "Parent Level", "Are you sure ?": "Are you sure ?", "Type": "Type", "Confidentiality Level": "Confidentiality Level", "Connection Security": "Connection Security", "Date": "Date", "Distribution List": "Distribution List", "Go Back": "Go Back", "loading": "loading", "As #{name}": "As #{name}", "General Bulk #{contactType}": "General Bulk #{contactType}", "Click to assign": "Click to assign", "Not assigned": "Not assigned", "Distribution List as #{name}": "Distribution List as #{name}", "Required": "Required", "Date Format": "Date Format", "Default Number of Cycles for Program": "Default Number of Cycles for Program", "year_count": "year_count", "month_count": "month_count", "Search": "Search", "Select Library": "Select Library", "Library": "Library", "Curriculum": "Curriculum", "Reason": "Reason", "Education Type": "Education Type", "Equivalent Program/Grade": "Equivalent Program/Grade", "Program Name": "Program Name", "Result": "Result", "Qualification Notes": "Qualification Notes", "Qualification": "Qualification", "Name": "Name", "Select File Import Type": "Select File Import Type", "For": "For", "No #{assessmentBoundaryPlural}": "No #{assessmentBoundaryPlural}", "No available #{assessmentBoundaryPlural}. Contact administrator for help": "No available #{assessmentBoundaryPlural}. Contact administrator for help", "Hours": "Hours", "None": "None", "loading...": "loading...", "Languages": "Languages", "Loading languages...": "Loading languages...", "Language": "Language", "Go to Site": "Go to Site", "Loading nationalities...": "Loading nationalities...", "Nationality": "Nationality", "Password": "Password", "Loading Assignees...": "Loading Assignees...", "Assignee": "Assignee", "Infotip Type": "Infotip Type", "Admin relation type": "Admin relation type", "Person Relation": "Person Relation", "Invalid #{phoneType} Number": "Invalid #{phoneType} Number", "Maximum #{maxLength} characters": "Maximum #{maxLength} characters", "All selected": "All selected", "selected": "selected", "Priority": "Priority", "Loading calendars...": "Loading calendars...", "Program Calendar": "Program Calendar", "Program Group Type": "Program Group Type", "Loading program groups...": "Loading program groups...", "Program Group": "Program Group", "Relation Priority": "Relation Priority", "Role": "Role", "Clear": "Clear", "Rounding": "Rounding", "Rule": "Rule", "Field Size in row": "Field Size in row", "Reserve Row": "Reserve Row", "Calculation Rule": "Calculation Rule", "Manage Growth": "Manage Growth", "Repository": "Repository", "Gray": "<PERSON>", "Bordered": "Bordered", "Spaced": "Spaced", "Uppercase": "Uppercase", "Big Red": "Big Red", "Small Blue": "Small Blue", "Time Format": "Time Format", "Start Time must be before or equal to End Time": "Start Time must be before or equal to End Time", "Loading templates...": "Loading templates...", "Weekday": "Weekday", "Clear above fields": "Clear above fields", "Position": "Position", "Type of Experience": "Type of Experience", "Month": "Month", "#{count} similar #{record} found": "#{count} similar #{record} found", "Please select one or click Close to continue saving the new record": "Please select one or click Close to continue saving the new record", "Company Name": "Company Name", "Company Phone": "Company Phone", "Company Email": "Company Email", "From": "From", "To": "To", "Years of Experience": "Years of Experience", "Notes": "Notes", "No Data": "No Data", "Add #{label}": "Add #{label}", "Reset fields": "Reset fields", "Reset": "Reset", "Remove #{code}?": "Remove #{code}?", "Are you sure you want to remove this #{type}?": "Are you sure you want to remove this #{type}?", "Delete": "Delete", "You don’t have permissions to view the page. You may contact your Organisation Administrator for Access.": "You don’t have permissions to view the page. You may contact your Organisation Administrator for Access.", "Continue": "Continue", "Apply Filters": "Apply Filters", "Click to download": "Click to download", "No more than #{allowedNumberOfFiles} #{fileTitle} allowed": "No more than #{allowedNumberOfFiles} #{fileTitle} allowed", "Photo exceeds the maximum allowed size of ${\n            round(maxSize / BYTES_IN_MEGABYTE, ROUND_TO) >= 1\n              ? ": "Photo exceeds the maximum allowed size of ${\n            round(maxSize / BYTES_IN_MEGABYTE, ROUND_TO) >= 1\n              ? ", "Resize": "Resize", "This attachment already exists, replace or add the new file": "This attachment already exists, replace or add the new file", "New file": "New file", "Replace": "Replace", "No less than #{minimumNumberOfFiles} files allowed": "No less than #{minimumNumberOfFiles} files allowed", "Back": "Back", "Zoom to fit": "Zoom to fit", "Zoom in": "Zoom in", "Zoom out": "Zoom out", "Download": "Download", "Previous image": "Previous image", "Next image": "Next image", "Attach File": "Attach File", "Max length is #{maxDescriptionLength}": "Max length is #{maxDescriptionLength}", "File Info": "File Info", "Split Audio": "Split Audio", "Image AI Improvement": "Image AI Improvement", "Sequence": "Sequence", "Content Category": "Content Category", "Caption": "Caption", "Content Keyword": "Content Keyword", "Preview": "Preview", "Type: #{fileFormat}": "Type: #{fileFormat}", "Size": "Size", "Owner": "Owner", "Created": "Created", "Description": "Description", "Replicate API key is not working.": "Replicate API key is not working.", "API credit has expired.": "API credit has expired.", "Data fetch failed, Please try again later!": "Data fetch failed, Please try again later!", "Version": "Version", "More": "More", "Generate": "Generate", "Enter prompt": "Enter prompt", "Error": "Error", "Start point is not valid.": "Start point is not valid.", "Enter at least one split point.": "Enter at least one split point.", "Process Split": "Process Split", "Confirm": "Confirm", "HH": "HH", "MM": "MM", "SS": "SS", "Person Photo": "Person Photo", "Upload Photo": "Upload Photo", "Delete Photo": "Delete Photo", "Undo Remove": "Undo Remove", "#{name} exceeds the maximum allowed size of #{maxSize}": "#{name} exceeds the maximum allowed size of #{maxSize}", "File type is not allowed.": "File type is not allowed.", "Ok": "Ok", "Your friends will be easier to find you if you upload your real photo": "Your friends will be easier to find you if you upload your real photo", "You can make an instant photo, if your device is equipped with a": "You can make an instant photo, if your device is equipped with a", "webcam": "webcam", "or": "or", "upload images": "upload images", "in JPG, GIF or PNG": "in JPG, GIF or PNG", "Drag & drop files here": "Drag & drop files here", "or click to select file": "or click to select file", "Take a picture": "Take a picture", "Browse file": "Browse file", "#{name} exceeds the maximum allowed size of #{maxSize}Mb": "#{name} exceeds the maximum allowed size of #{maxSize}Mb", "Uploading #{name}": "Uploading #{name}", "File Name": "File Name", "Drag and drop files here": "Drag and drop files here", "This file type is not allowed": "This file type is not allowed", "Subject": "Subject", "Reply-To": "Reply-To", "Expiry in": "Expiry in", "Custom Date": "Custom Date", "Field Label": "Field Label", "Attachment": "Attachment", "Field Properties": "Field Properties", "Is Mandatory": "Is Mandatory", "Has Tooltip": "<PERSON>", "Tooltip": "<PERSON><PERSON><PERSON>", "Field Layout": "Field Layout", "Responsive": "Responsive", "Option": "Option", "Default": "<PERSON><PERSON><PERSON>", "Asset category": "Asset category", "Asset condition": "Asset condition", "Asset group": "Asset group", "View": "View", "Log Type": "Log Type", "Communication Status": "Communication Status", "Communication type": "Communication type", "Sort": "Sort", "Loading": "Loading", "There is no data matching your criteria": "There is no data matching your criteria", "Resources": "Resources", "Resource": "Resource", "Log Date": "Log Date", "Item": "<PERSON><PERSON>", "Group by": "Group by", "Intake": "Intake", "None Selected": "None Selected", "All": "All", "#{count} selected": "#{count} selected", "Select All": "Select All", "Person Entity": "Person Entity", "Other Entities": "Other Entities", "#{name}": "#{name}", "Class Status": "Class Status", "Enrolment Status": "Enrolment Status", "Program Structure": "Program Structure", "Program": "Program", "Institution type": "Institution type", "Platform": "Platform", "Year": "Year", "Toggle Filters": "Toggle Filters", "Creating": "Creating", "Crop Photo": "Crop Photo", "Crop": "Crop", "Drag and drop image here or": "Drag and drop image here or", "Browse": "Browse", "No more than #{allowedNumberOfFiles} files allowed": "No more than #{allowedNumberOfFiles} files allowed", "Uploading...": "Uploading...", "Report Message": "Report Message", "Report": "Report", "Organisations": "Organisations", "Add #{name}": "Add #{name}", "#{prefix}Infotip": "#{prefix}Infotip", "Acknowledgements": "Acknowledgements", "Infotip": "Infotip", "Show Infotip In": "Show Infotip In", "LMS": "LMS", "Popovers": "Popovers", "Feedback": "<PERSON><PERSON><PERSON>", "Read Receipt": "Read Receipt", "Acknowledge": "Acknowledge", "Comment": "Comment", "Inactivate After": "Inactivate After", "Acknowledgement can be between #{min} and #{max}": "Acknowledgement can be between #{min} and #{max}", "Infotips": "Infotips", "Open": "Open", "Marital Status": "Marital Status", "Title": "Title", "#{count} similar record#{s} found": "#{count} similar record#{s} found", "People with this address": "People with this address", "checking...": "checking...", "#{number} submissions due": "#{number} submissions due", "No submissions due": "No submissions due", "Add new files": "Add new files", "Drag and Drop Files Here to Upload": "Drag and Drop Files Here to Upload", "The audio quality is acceptable.": "The audio quality is acceptable.", "The audio quality is not good. Would you like to convert the audio to improve the quality?": "The audio quality is not good. Would you like to convert the audio to improve the quality?", "Convert": "Convert", "Audio Validation": "Audio Validation", "Sample": "<PERSON><PERSON>", "#{fileName} exceeds the maximum allowed size of #{maxSize}": "#{fileName} exceeds the maximum allowed size of #{maxSize}", "AI": "AI", "Age Group": "Age Group", "Gender": "Gender", "Speaker": "Speaker", "Style": "Style", "Speed": "Speed", "Pitch": "Pitch", "Volume Gain": "Volume Gain", "Frequency": "Frequency", "Audio exceeds the maximum allowed size of #{maxSize}": "Audio exceeds the maximum allowed size of #{maxSize}", "Are you sure you want to delete #{fileName} ?": "Are you sure you want to delete #{fileName} ?", "Your device doesn": "Your device doesn", "Stop": "Stop", "Record": "Record", "#{time}/#{maximumTime}": "#{time}/#{maximumTime}", "Record New": "Record New", "#{title} (#{maxSize})": "#{title} (#{maxSize})", "#{title}": "#{title}", "#{totalExceededFiles} images exceeds the maximum allowed size of #{maxSize}": "#{totalExceededFiles} images exceeds the maximum allowed size of #{maxSize}", "#{totalExceededFiles} files exceeds the maximum allowed size of #{maxSize}": "#{totalExceededFiles} files exceeds the maximum allowed size of #{maxSize}", "The file format is not allowed for attachment.": "The file format is not allowed for attachment.", "Converting wma file into mp3 file, it may take few moments.": "Converting wma file into mp3 file, it may take few moments.", "Failed to convert audio": "Failed to convert audio", "The duration of the #{type} shouldn": "The duration of the #{type} shouldn", "Upload": "Upload", "Text to Audio": "Text to Audio", "Take Photo": "Take Photo", "Record Video": "Record Video", "Record Audio": "Record Audio", "Photo exceeds the maximum allowed size of #{maxSize}": "Photo exceeds the maximum allowed size of #{maxSize}", "Capture": "Capture", "Retake Picture": "Retake Picture", "Video exceeds the maximum allowed size of #{maxSize}": "Video exceeds the maximum allowed size of #{maxSize}", "Colour": "Colour", "up to #{endDate}": "up to #{endDate}", "#{startDate} - ongoing": "#{startDate} - ongoing", "Unset": "Unset", "up to #{startTime}": "up to #{startTime}", "ongoing": "ongoing", "Clear Color Selection": "Clear Color Selection", "cancel": "cancel", "choose": "choose", "Today": "Today", "Yesterday": "Yesterday", "This Week": "This Week", "This Month": "This Month", "This Year": "This Year", "Time": "Time", "None selected": "None selected", "Some selected": "Some selected", "Select One": "Select One", "Type the keyword and press Enter to save it": "Type the keyword and press Enter to save it", "Tags": "Tags", "#{length}/#{maxLength}": "#{length}/#{maxLength}", "Type something": "Type something", "#{length} images exceed the maximum allowed size #{maxSize}": "#{length} images exceed the maximum allowed size #{maxSize}", "Resize All": "Resize All", "The upload of #{failedImages} image(s) failed. Please try again.": "The upload of #{failedImages} image(s) failed. Please try again.", "There are no available merge-codes in tenant modules.": "There are no available merge-codes in tenant modules.", "Other": "Other", "Merge Codes": "Merge Codes", "Start": "Start", "End": "End", "Week": "Week", "Day": "Day", "List": "List", "Add File": "Add File", "Location": "Location", "Modified": "Modified", "Updated Successfully": "Updated Successfully", "Deleted Succesfully": "Deleted Succesfully", "Load More Results": "Load More Results", "Are you sure you want to delete #{name}?": "Are you sure you want to delete #{name}?", "Add option": "Add option", "Edit option": "Edit option", "Select at least one column to calculate ": "Select at least one column to calculate ", "Set up ": "Set up ", "Calculations": "Calculations", "New Weight": "New Weight", "More than #{MAX_NESTED_CATEGORIES} level categories not allowed.": "More than #{MAX_NESTED_CATEGORIES} level categories not allowed.", "Created Successfully": "Created Successfully", "Add Category": "Add Category", "Category Name": "Category Name", "Abbreviation": "Abbreviation", "Calculate": "Calculate", "Calculations method below will be deleted from the mark book, proceed?": "Calculations method below will be deleted from the mark book, proceed?", "Content Level": "Content Level", "Item Level": "Item Level", "Select at least one column to calculate": "Select at least one column to calculate", "Configure the ": "Configure the ", "Column Heading": "Column <PERSON>ing", "Weight": "Weight", "Add Calculation": "Add Calculation", "Calculation": "Calculation", "Best": "Best", "Are you sure you want to perform action? The system should remove all marks with previous assessment type.": "Are you sure you want to perform action? The system should remove all marks with previous assessment type.", "Assessed": "Assessed", "Assessment type": "Assessment type", "Assessment label": "Assessment label", "Tick Style": "Tick Style", "Number of characters": "Number of characters", "Emoticon": "Emoticon", "Options set": "Options set", "Options": "Options", "Maximum": "Maximum", "Copy Rubric From": "<PERSON><PERSON> R<PERSON>ric <PERSON>", "Rubric": "<PERSON><PERSON><PERSON>", "Copy Rubric": "<PERSON><PERSON>", "Copy function only available when there is no rubric setup": "Copy function only available when there is no rubric setup", "Enter notes for students": "Enter notes for students", "capability": "capability", "Rubric is already used for assessment and cannot be modified in structure": "Rubric is already used for assessment and cannot be modified in structure", "Add a new row for capability": "Add a new row for capability", "criteria": "criteria", "0,00": "0,00", "#{parsedCriteriaPointsFrom} #{point}": "#{parsedCriteriaPointsFrom} #{point}", " #{parsedCriteriaPointsFrom}": " #{parsedCriteriaPointsFrom}", " - #{parsedCriteriaPointsTo} #{point}": " - #{parsedCriteriaPointsTo} #{point}", "#{value} %": "#{value} %", " #{value}": " #{value}", " - #{parsedCriteriaPointsTo}%": " - #{parsedCriteriaPointsTo}%", "level description": "level description", "Capability Score": "Capability Score", "Resulted Points": "Resulted Points", "Result: #{score}#{percentageSymbol} out of #{highestScore}#{percentageSymbol}": "Result: #{score}#{percentageSymbol} out of #{highestScore}#{percentageSymbol}", "Copy to Mark Assigned": "Copy to Mark <PERSON>", "Add a new row for skill": "Add a new row for skill", "Add a new column for the level description": "Add a new column for the level description", "skill": "skill", "#{length} contents already exist, you can": "#{length} contents already exist, you can", "#{length} items already exist, you can": "#{length} items already exist, you can", "Remove ": "Remove ", "Are you sure you want to remove this content?": "Are you sure you want to remove this content?", "Updated Succesfully": "Updated Succesfully", "Removed Successfully": "Removed Successfully", "Added Successfully": "Added Successfully", "Add Content": "Add Content", "No content added": "No content added", "Add Item": "Add Item", "Are you sure you want to remove this item?": "Are you sure you want to remove this item?", "#{taskName} Name": "#{taskName} Name", "Short Description": "Short Description", "Search Students": "Search Students", "Group Submission": "Group Submission", "Availability Rule": "Availability Rule", "Availability Date": "Availability Date", "Due Date Rule": "Due Date Rule", "Due On": "Due On", "Allow Submissions Until": "Allow Submissions Until", "Submission Type": "Submission Type", "Text Submission": "Text Submission", "Attachments": "Attachments", "Number of attachments": "Number of attachments", "Audio Submissions (maximum 5 minutes)": "Audio Submissions (maximum 5 minutes)", "Number of audio submissions": "Number of audio submissions", "Video submissions (maximum 50 MB)": "Video submissions (maximum 50 MB)", "Number of video submissions": "Number of video submissions", "Rules": "Rules", "Multiple Submissions": "Multiple Submissions", "Notifications": "Notifications", "Email/Notify #{participant} when marked": "Email/Notify #{participant} when marked", "Email/Notify #{participant}’s relation when marked": "Email/Notify #{participant}’s relation when marked", "Copy of": "Copy of", "Changing the eContent integration will result in all data in the Content tab being removed.": "Changing the eContent integration will result in all data in the Content tab being removed.", "Add #{taskTypeName}": "Add #{taskTypeName}", "Students": "Students", "Submission": "Submission", "Assessment": "Assessment", "Learning": "Learning", "Content": "Content", "To add tasks, you should select at least one task type on LMS tab of Subject": "To add tasks, you should select at least one task type on LMS tab of Subject", "Add Task": "Add Task", "Type a message": "Type a message", "Send message": "Send message", "Edit Message": "Edit Message", "Load more": "Load more", "0.5x": "0.5x", "1x": "1x", "1.5x": "1.5x", "2x": "2x", "Edit": "Edit", "Reply": "Reply", "Playback speed": "Playback speed", "Copy": "Copy", "There is a problem with accessing the microphone": "There is a problem with accessing the microphone", "Cancel recording": "Cancel recording", "Send audio message": "Send audio message", "Recording": "Recording", "Recording... #{time}": "Recording... #{time}", "Uploading... #{time}": "Uploading... #{time}", "Are you sure you want to delete this message?": "Are you sure you want to delete this message?", "Delete for other participants as well": "Delete for other participants as well", "Delete for #{firstName} #{lastName} as well": "Delete for #{firstName} #{lastName} as well", "Voice Message": "Voice Message", "Attachments: #{count}": "Attachments: #{count}", "Delete identity?": "Delete identity?", "Biography": "Biography", "Identities": "Identities", "Identity Number": "Identity Number", "Valid To": "<PERSON><PERSON>", "Nationalities": "Nationalities", "Level": "Level", "Languages Speaking at Home": "Languages Speaking at Home", "Religion": "Religion", "Profile": "Profile", "About #{firstName}": "About #{firstName}", "Default Timezone": "Default Timezone", "Contacts": "Contacts", "Quick Link Name": "Quick Link Name", "enter description (optional)": "enter description (optional)", "Edit Quick Link": "Edit Quick Link", "Add to Quick Links in My Space": "Add to Quick Links in My Space", "Add to Quick Links": "Add to Quick Links", "Delete #{assetName} ?": "Delete #{assetName} ?", "Delete #{assetName}": "Delete #{assetName}", "Are you sure you want to delete this #{name}?": "Are you sure you want to delete this #{name}?", "Cancelled": "Cancelled", "Your work is safe :)": "Your work is safe :)", "#{assetName} Deleted!": "#{assetName} Deleted!", "Delete File ?": "Delete File ?", "Delete File": "Delete File", "Are you sure you want to delete this file from the ed-drive?": "Are you sure you want to delete this file from the ed-drive?", "File Deleted!": "File Deleted!", "Input Mask: #{inputMask}": "Input Mask: #{inputMask}", "Use the following to format and validate input": "Use the following to format and validate input", "0 for a number": "0 for a number", "A for upper case letter": "A for upper case letter", "a for lowercase letter": "a for lowercase letter", "* for any alphanumeric character like 9a8bc ": "* for any alphanumeric character like 9a8bc ", "0 for date like 00/00/0000": "0 for date like 00/00/0000", "use any non-alpha character of or space as separator": "use any non-alpha character of or space as separator", "Showing #{first} to #{last} of #{count} entries": "Showing #{first} to #{last} of #{count} entries", "Are you sure you want to delete this link?": "Are you sure you want to delete this link?", "Quicklinks": "Quicklinks", "Updated successfully": "Updated successfully", "Select how you like to see Quick Link pages:": "Select how you like to see Quick Link pages:", "more": "more", "N/A": "N/A", "Something went wrong": "Something went wrong", "Please try again later": "Please try again later", "Network error": "Network error", "Internet seems not accessible, please try again later": "Internet seems not accessible, please try again later", "Stick": "Stick", "Unstick": "Unstick", "No Infotips": "No Infotips", "Joined On": "Joined On", "Leaving Date": "Leaving Date", "Loading Person Info...": "Loading Person Info...", "nationality_#{name}": "nationality_#{name}", "should be a number": "should be a number", "should not be negative": "should not be negative", "should be positive": "should be positive", "should have at most #{maxDigits} digits": "should have at most #{maxDigits} digits", "should have only #{digitsCount} digits": "should have only #{digitsCount} digits", " can be between #{minValue} and #{maxValue}": " can be between #{minValue} and #{maxValue}", "Maximum  characters": "Maximum  characters", "Not Unique": "Not Unique", "Switch Language": "Switch Language", "Select Language": "Select Language", "Current Language": "Current Language", "Logout": "Logout", "Privacy Policy": "Privacy Policy", "Terms of Use": "Terms of Use", "© Copyright #{year} Gowell Solutions Pty Ltd": "© Copyright #{year} Gowell Solutions Pty Ltd", "Accounts": "Accounts", "Edana Menu": "<PERSON><PERSON>", "Organisation Selector": "Organisation Selector", "Settings": "Settings", "Something goes wrong. You may reload the page": "Something goes wrong. You may reload the page", "Login Cancelled": "<PERSON><PERSON>ed", "Google login popup is blocked by browser. Please allow popups to continue.": "Google login popup is blocked by browser. Please allow popups to continue.", "y": "y", "m": "m", "days ago": "days ago", "#{week} ago": "#{week} ago", "days": "days", "months": "months", "years": "years", "In a few seconds": "In a few seconds", "In #{min} min": "In #{min} min", "In #{mins} mins": "In #{mins} mins", "In #{hr} hr": "In #{hr} hr", "In #{hrs} hrs": "In #{hrs} hrs", "Tomorrow, #{time}": "Tomorrow, #{time}", "Just now": "Just now", "#{min} min ago": "#{min} min ago", "#{mins} mins ago": "#{mins} mins ago", "#{hr} hr ago": "#{hr} hr ago", "#{hrs} hrs ago": "#{hrs} hrs ago", "Yesterday, #{time}": "Yesterday, #{time}", "Incorrect date. It": "Incorrect date. It", "and": "and", "Show previous #{count} out of #{restCount}": "Show previous #{count} out of #{restCount}", "Show next #{count} out of #{restCount}": "Show next #{count} out of #{restCount}", "Sunday": "Sunday", "Monday": "Monday", "Tuesday": "Tuesday", "Wednesday": "Wednesday", "Thursday": "Thursday", "Friday": "Friday", "Saturday": "Saturday", "Sun": "Sun", "Mon": "Mon", "Tue": "<PERSON><PERSON>", "Wed": "Wed", "Thu": "<PERSON>hu", "Fri": "<PERSON><PERSON>", "Sat": "Sat", "Su": "Su", "Mo": "Mo", "Tu": "Tu", "We": "We", "Th": "Th", "Fr": "Fr", "Sa": "Sa", "January": "January", "February": "February", "March": "March", "April": "April", "May": "May", "June": "June", "July": "July", "August": "August", "September": "September", "October": "October", "November": "November", "December": "December", "Jan": "Jan", "Feb": "Feb", "Mar": "Mar", "Apr": "Apr", "Jun": "Jun", "Jul": "Jul", "Aug": "Aug", "Sep": "Sep", "Oct": "Oct", "Nov": "Nov", "Dec": "Dec", "Months": "Months", "Tomorrow": "Tomorrow", "Send Recovery Instructions": "Send Recovery Instructions", "Password Recovery": "Password Recovery", "Email address is required in order to reset your password": "Email address is required in order to reset your password", "Email": "Email", "Password Reset Success": "Password Reset Success", "Your password has been changed successfully.": "Your password has been changed successfully.", "Password recovery": "Password recovery", "Recovery instructions has been emailed to you, check your inbox and otherwise your junkmail folder": "Recovery instructions has been emailed to you, check your inbox and otherwise your junkmail folder", "Reset Password": "Reset Password", "Enter new password information": "Enter new password information", "New Password": "New Password", "Retype New Password": "Retype New Password", "Mobile": "Mobile", "Please check your #{name} for confirmation code": "Please check your #{name} for confirmation code", "Confirmation Required": "Confirmation Required", "#{name} Confirmation Token": "#{name} Confirmation Token", "You have no active entity allocation or active role that gives at least one permission.": "You have no active entity allocation or active role that gives at least one permission.", "email": "email", "cell": "cell", "LOGIN OTP IS REQUIRED FOR LOGIN": "LOGIN OTP IS REQUIRED FOR LOGIN", "It has been sent to your primarily #{entity}": "It has been sent to your primarily #{entity}", "Enter Login OTP Here": "Enter Login OTP Here", "Login": "<PERSON><PERSON>", "Forgot password?": "Forgot password?", "Login to #{siteName}": "Login to #{siteName}", "Proceed with Login": "Proceed with <PERSON><PERSON>", "I agree fully with the above conditions": "I agree fully with the above conditions", "Please reset your password to continue.": "Please reset your password to continue.", "Password is expired!": "Password is expired!", "Save Password": "Save Password", "Enter password information": "Enter password information", "Setup your password": "Setup your password", "Retype Password": "Retype Password", "Password Setup Success": "Password Setup Success", "Your password has been set successfully.": "Your password has been set successfully.", "Something goes wrong. You may reload the page.": "Something goes wrong. You may reload the page.", "Application has been updated, the page will be reloaded in #{timeLeft} seconds": "Application has been updated, the page will be reloaded in #{timeLeft} seconds", "#{length} #{type} Long": "#{length} #{type} Long", "Must be #{length} #{type} or more": "Must be #{length} #{type} or more", "#{length} Lowercase #{type}": "#{length} Lowercase #{type}", "Must be #{length} lowercase #{type} or more": "Must be #{length} lowercase #{type} or more", "#{length} Uppercase #{type}": "#{length} Uppercase #{type}", "Must be #{length} uppercase #{type} or more": "Must be #{length} uppercase #{type} or more", "#{length} #{type}": "#{length} #{type}", "#{length} Special #{type} !#%$@&^": "#{length} Special #{type} !#%$@&^", "Must be #{length} special #{type} or more": "Must be #{length} special #{type} or more", "Same Passwords": "Same Passwords", "Passwords do not match": "Passwords do not match", "Passwords are required": "Passwords are required"}, "CLIENTPORTAL": {}, "ECONTENT": {"Libraries": "Libraries", "#{resourcesCount} #{resource}": "#{resourcesCount} #{resource}", "Add Item": "Add Item", "Add at least one resource to the library before creating the item": "Add at least one resource to the library before creating the item", "There are no texts": "There are no texts", "There are multiple contents with the same language": "There are multiple contents with the same language", "Text sequences related to ": "Text sequences related to ", "Text sequences related to the below contents are not unique:\\n#{contents}": "Text sequences related to the below contents are not unique:\\n#{contents}", "Details": "Details", "Contents List": "Contents List", "Cancel": "Cancel", "Next": "Next", "Resource structure of the selected item does not support the #{languages} Language.": "Resource structure of the selected item does not support the #{languages} Language.", "Ok": "Ok", "Resource structure of the #{length} contents does not support.": "Resource structure of the #{length} contents does not support.", "#{error}": "#{error}", "Moved Succesfully": "Moved Succesfully", "Last updated by": "Last updated by", "Move Contents": "Move Contents", "Add Content": "Add Content", "Move": "Move", "Updated Succesfully": "Updated Succesfully", "Resource structure of the selected item does not support the #{name} Language.": "Resource structure of the selected item does not support the #{name} Language.", "Resource structure of the selected item does not support the Text.": "Resource structure of the selected item does not support the Text.", "Resource structure of the selected item does not support #{type} Text.": "Resource structure of the selected item does not support #{type} Text.", "Resource structure of the selected item does not support Simple Text.": "Resource structure of the selected item does not support Simple Text.", "Resource structure of the selected item does not support Rich Text.": "Resource structure of the selected item does not support Rich Text.", "Language": "Language", "Remove": "Remove", "Are you sure you want to remove the log?": "Are you sure you want to remove the log?", "Removed Successfully": "Removed Successfully", "Add Manual Log": "Add Manual Log", "Add Audio File": "Add Audio File", "Add Document": "Add Document", "Error": "Error", "Created Successfully": "Created Successfully", "Updated Successfully": "Updated Successfully", "Create and Add New Content": "Create and Add New Content", "Create and Add New Item": "Create and Add New Item", "None": "None", "Sequence": "Sequence", "Description": "Description", "Framework": "Framework", "Author": "Author", "Source": "Source", "Topic Keyword": "Topic Keyword", "All": "All", "No Category": "No Category", "Update": "Update", "Content": "Content", "Questions": "Questions", "Audit Log": "<PERSON>t Log", "Update Manual Log": "Update Manual Log", "Add": "Add", "Log Details": "Log Details", "Add Log Details": "Add Log Details", "Type": "Type", "Size": "Size", "Length": "Length", "Date Uploaded": "Date Uploaded", "Add Image": "Add Image", "Close": "Close", "#{model} API key is not working.": "#{model} API key is not working.", "Data fetch failed, Please try again later!": "Data fetch failed, Please try again later!", "The response of #{name} was not in JSON format. Please try again.": "The response of #{name} was not in JSON format. Please try again.", "There was a problem uploading questions": "There was a problem uploading questions", "Cancel Upload": "Cancel Upload", "Retry": "Retry", "More": "More", "Selected #{totalTicked} of #{total}": "Selected #{totalTicked} of #{total}", "Add and Close": "Add and Close", "Add All": "Add All", "Clear All": "Clear All", "Clear": "Clear", "Search": "Search", "Add Selected": "Add Selected", "Response": "Response", "Select text from above or enter new text": "Select text from above or enter new text", "Add to Content": "Add to Content", "Question": "Question", "Are you sure you want to remove this question?": "Are you sure you want to remove this question?", "Correct Answer": "Correct Answer", "Enter the correct numeric answer": "Enter the correct numeric answer", "Enter the correct text answer": "Enter the correct text answer", "Maximum Number of Words": "Maximum Number of Words", "Save and Close": "Save and Close", "Save": "Save", "Enter question here": "Enter question here", "Images": "Images", "Options": "Options", "More than one correct answer": "More than one correct answer", "Warning: Multiple correct selected, but only one of them can be selected by user": "Warning: Multiple correct selected, but only one of them can be selected by user", "Difficulty Level": "Difficulty Level", "Points": "Points", "Content Keyword": "Content Keyword", "Enter Question": "Enter Question", "Enter correct answer as a number": "Enter correct answer as a number", "Enter option": "Enter option", "Enter true/false question": "Enter true/false question", "Remove Option": "Remove Option", "True": "True", "False": "False", "Simple": "Simple", "Save Next": "Save Next", "Save Select": "Save Select", "Difficulty level should be between #{MIN_LEVEL} - #{MAX_LEVEL}": "Difficulty level should be between #{MIN_LEVEL} - #{MAX_LEVEL}", "Add #{name} Question": "Add #{name} Question", "Preview": "Preview", "PDF": "PDF", "#{name} Question": "#{name} Question", "Save  Mode": "Save  Mode", "Edit": "Edit", "Image": "Image", "Add Question (#{questionTypeTitle})": "Add Question (#{questionTypeTitle})", "View Question (#{questionTypeTitle})": "View Question (#{questionTypeTitle})", "Edit Question (#{questionTypeTitle})": "Edit Question (#{questionTypeTitle})", "View Mode": "View Mode", "Edit Mode": "Edit Mode", "Quick Add": "Quick Add", "Auto Generate Question": "Auto Generate Question", "Preview Questions": "Preview Questions", "Title": "Title", "Option": "Option", "Removed Succesfully": "Removed Succesfully", "Saved Successfully": "Saved Successfully", "Copied to Clipboard": "Copied to Clipboard", "Added Successfully": "Added Successfully", "Copy": "Copy", "Copy to Clipboard": "Copy to Clipboard", "Copy Simple": "Copy Simple", "Copy Formatted": "Copy Formatted", "Transform": "Transform", "Simple Text Box": "Simple Text Box", "Rich Text Box": "Rich Text Box", "Add Text": "Add Text", "Content Category": "Content Category", "Text Content": "Text Content", "Transformation": "Transformation", "Version": "Version", "Please enter a prompt first.": "Please enter a prompt first.", "Please enter a prompt and text first.": "Please enter a prompt and text first.", "The number of characters (#{length}) exceeds the maximum allowed characters (#{maxLength}) for the content. Please note that if you proceed, not all the text can be added to the content, and it will be truncated.": "The number of characters (#{length}) exceeds the maximum allowed characters (#{maxLength}) for the content. Please note that if you proceed, not all the text can be added to the content, and it will be truncated.", "Proceed": "Proceed", "The number of characters (#{length}) exceeds the maximum allowed characters ($#maxLength}) for the content. Please note that if you proceed, not all the text can be added to the content, and it will be truncated.": "The number of characters (#{length}) exceeds the maximum allowed characters ($#maxLength}) for the content. Please note that if you proceed, not all the text can be added to the content, and it will be truncated.", "Update Content": "Update Content", "Predefined": "Predefined", "Enter text here": "Enter text here", "Custom": "Custom", "Enter custom prompt here": "Enter custom prompt here", "Add URL": "Add URL", "URL": "URL", "URL name": "URL name", "URL Caption": "URL Caption", "Open Link": "Open Link", "Copy Link": "Copy Link", "Add Video File": "Add Video File", "Item Name": "Item Name", "Resource": "Resource", "Delete": "Delete", "Go Back": "Go Back", "Google Translate": "Google Translate", "Edit Content": "Edit Content", "Translation Support": "Translation Support", "Summary": "Summary", "Compare": "Compare", "There is no more than one language text content": "There is no more than one language text content", "Maximum of #{MAX_SELECTABLE_LANUGAGES} languages allowed": "Maximum of #{MAX_SELECTABLE_LANUGAGES} languages allowed", "Translation": "Translation", "#{name} (#{count})": "#{name} (#{count})", "There is no data matching your criteria": "There is no data matching your criteria", "Content not found": "Content not found", "Items": "Items"}, "EDCOM": {"Entity Type": "Entity Type", "Messages": "Messages", "Conversations": "Conversations", "Emails": "Emails", "Individuals": "Individuals", "Add #{name}": "Add #{name}", "Contact": "Contact", "Persons": "Persons", "Delete for other participants as well": "Delete for other participants as well", "Are you sure you want to delete this Conversation?": "Are you sure you want to delete this Conversation?", "Edit": "Edit", "Conversation": "Conversation", "Search Mode": "Search Mode", "Cancel": "Cancel", "Create": "Create", "Update": "Update", "Topic": "Topic", "Conversation Area": "Conversation Area", "Active Duration": "Active Duration", "Status": "Status", "Edited: ": "Edited: ", "Delete": "Delete", "Next": "Next", "Updated successfully": "Updated successfully", "Owner": "Owner", "You may no longer be able to change settings, and the new owner may also remove your access.": "You may no longer be able to change settings, and the new owner may also remove your access.", "Yes": "Yes", "Make Owner": "Make Owner", "Search for Participants": "Search for Participants", "Copy": "Copy", "Go to Conversation Tab": "Go to Conversation Tab", "Are you sure you want to delete this message?": "Are you sure you want to delete this message?", "Report": "Report", "Reply": "Reply", "Quote": "Quote", "#{count} Replies": "#{count} Replies", "Unread Messages": "Unread Messages", "Show previous #{count} out of #{restCount} replies": "Show previous #{count} out of #{restCount} replies", "Are you sure you want to delete #{name}?": "Are you sure you want to delete #{name}?", "Open": "Open", "There is no active staff person entity allocation.": "There is no active staff person entity allocation.", "Contacts": "Contacts", "Entity": "Entity", "Date Published": "Date Published", "Select a/an #{name} item to view or add a new one using Add #{name} button.": "Select a/an #{name} item to view or add a new one using Add #{name} button.", "Send this email to #{totalRecipients} #{recipient}?": "Send this email to #{totalRecipients} #{recipient}?", "Email gateway is not responding would you like to still queue the message?": "Email gateway is not responding would you like to still queue the message?", "No": "No", "Send": "Send", "Close": "Close", "Preview": "Preview", "Subject": "Subject", "Email Content": "Email Content", "Attach File": "Attach File", "Attachment": "Attachment", "Search for Recipients": "Search for Recipients", "Depends on the settings of the SMTP server. Make sure the #{type} option is enabled on the SMTP server.": "Depends on the settings of the SMTP server. Make sure the #{type} option is enabled on the SMTP server.", "Email Confirmation": "Email Confirmation", "Delivery Receipt Confirmation": "Delivery Receipt Confirmation", "Read Receipt Confirmation": "Read Receipt Confirmation", "Send Status": "Send Status", "Date Scheduled to Send": "Date Scheduled to Send", "#{deliveredMessagesCount}/#{totalRecipients} emails have already been sent. Would you like to resume sending email to the remaining #{remainingRecipientsCount} recipients?": "#{deliveredMessagesCount}/#{totalRecipients} emails have already been sent. Would you like to resume sending email to the remaining #{remainingRecipientsCount} recipients?", "Proceed": "Proceed", "Cancel email? You will not be able to edit this email further": "Cancel email? You will not be able to edit this email further", "Email has already been sent to #{deliveredMessagesCount} recipients, this will resend the email to all from the start, proceed?": "Email has already been sent to #{deliveredMessagesCount} recipients, this will resend the email to all from the start, proceed?", "Go Back": "Go Back", "Cancel Scheduled Email": "Cancel Scheduled Email", "Stop Sending": "Stop Sending", "Cancel Email": "Cancel Email", "Resume Sending": "Resume Sending", "Resend": "Resend", "Retry Sending": "Retry Sending", "#{sent} recipient(s) out of #{total} received at least one email.": "#{sent} recipient(s) out of #{total} received at least one email.", "Draft": "Draft", "Clear": "Clear", "Email Categories": "Email Categories", "Category": "Category", "Contact Type": "Contact Type", "History": "History", "Message Type": "Message Type", "Deleted Successfully": "Deleted Successfully", "News": "News", "Newsletter": "Newsletter", "Audience": "Audience", "Viewed": "Viewed", "#{read} audience(s) out of #{total} received the news.": "#{read} audience(s) out of #{total} received the news.", "Categories": "Categories", "Search for Audiences": "Search for Audiences", "Headline": "Headline", "Content": "Content", "This news  item will be on the featured news with this image as a featured image, proceed?": "This news  item will be on the featured news with this image as a featured image, proceed?", "Set as Thumbnail": "Set as <PERSON><PERSON><PERSON>nail", "Click on Create/Update to add this picture as #{type}": "Click on Create/Update to add this picture as #{type}", "Thumbnail": "<PERSON><PERSON><PERSON><PERSON>", "Set as Featured Image": "Set as Featured Image", "Featured Image": "Featured Image", "Thumbnail Image": "Thumbnail Image", "This news is marked as a featured news by #{by} on #{on}": "This news is marked as a featured news by #{by} on #{on}", "Notification": "Notification", "Read Notifications": "Read Notifications", "Notifications": "Notifications", "#{sent} recipients out of #{total} received the notification": "#{sent} recipients out of #{total} received the notification", "Notification Content": "Notification Content", "Notification Categories": "Notification Categories", "Success": "Success", "Notification is sending": "Notification is sending", "Created successfully": "Created successfully", "Notifications is cancelled": "Notifications is cancelled", "Max length is #{maxDescriptionLength}": "Max length is #{maxDescriptionLength}", "Caption": "Caption", "Date Sent": "Date Sent", "Priority": "Priority", "Send Priority": "Send Priority", "Details": "Details", "#{count} #{label}": "#{count} #{label}", "Loading statistic": "Loading statistic"}, "EDDRIVE": {"My Drive": "My Drive", "Public Drive - #{petName}": "Public Drive - #{petName}", "Add Folder": "Add Folder", "Create": "Create", "Folder Name": "Folder Name", "Attached Successfully": "Attached Successfully", "Created Successfully": "Created Successfully", "Updated Successfully": "Updated Successfully", "Upload File": "Upload File", "Toggle View": "Toggle View", "Moved Successfully": "Moved Successfully", "Search": "Search", "Removed Successfully": "Removed Successfully", "Remove ": "Remove ", "Are you sure you want to remove this folder?": "Are you sure you want to remove this folder?", "Remove": "Remove", "Cancel": "Cancel", "Are you sure you want to remove this file?": "Are you sure you want to remove this file?", "Download": "Download", "Info": "Info", "Move": "Move", "Rename": "<PERSON><PERSON>", "Load More Results": "Load More Results", "Folders (#{foldersCount})": "Folders (#{foldersCount})", "Files (#{filesCount})": "Files (#{filesCount})", "Type": "Type", "Size": "Size", "Length": "Length", "Updated": "Updated", "Open": "Open", "Org Group": "Org Group", "Move #{name}": "Move #{name}", "Rename File": "Rename File", "Save": "Save", "File Name": "File Name", "Rename Folder": "<PERSON><PERSON>"}, "ERROR": {"This #{type} is under construction. Please check back soon!": "This #{type} is under construction. Please check back soon!", "Error page": "Error page"}, "FACILITYMANAGEMENT": {"Organisation": "Organisation", "Location": "Location", "Asset": "<PERSON><PERSON>", "#{organisationGroupName} Asset register": "#{organisationGroupName} Asset register", "Asset condition": "Asset condition", "Assessed by": "Assessed by", "Assessed on": "Assessed on", "Will be shown after update": "Will be shown after update", "Asset Name": "Asset Name", "Asset Code": "Asset Code", "Asset Condition": "Asset Condition", "Note": "Note", "Search": "Search", "Open": "Open", "Code": "Code", "Asset name": "Asset name", "Grouping": "Grouping", "Category": "Category", "Condition": "Condition", "N/A": "N/A"}, "LMSADMIN": {"Program Intake": "Program Intake", "N/A": "N/A", "Starting #{cycle}, #{year}": "Starting #{cycle}, #{year}", "Enrolment #{status}": "Enrolment #{status}", "Loading Intake Students list": "Loading Intake Students list", "Search": "Search", "Subject Enrolment Date": "Subject Enrolment Date", "updated #{time}": "updated #{time}", "#{entityTitle} Enrolment Status": "#{entityTitle} Enrolment Status", "Open": "Open", "Delete": "Delete", "Participant Information": "Participant Information", "Clear": "Clear", "New Enrolment": "New Enrolment", "Add": "Add", "Students": "Students", "Details": "Details", "Program Structure": "Program Structure", "Enrolments": "Enrolments", "Intake Name": "Intake Name", "Enrolment Period": "Enrolment Period", "Enrolment Status": "Enrolment Status", "Intake Status": "Intake Status", "#{participant} #{group}": "#{participant} #{group}", "#{name} #{programGroupTypeName} Enrolments": "#{name} #{programGroupTypeName} Enrolments", "Are you sure you want to delete #{fullName} ?": "Are you sure you want to delete #{fullName} ?", "Cancel": "Cancel", "Enrollment period has ended": "Enrollment period has ended", "The intake status is completed or deleted": "The intake status is completed or deleted", "Add Students": "Add Students", "#{name}": "#{name}", "Apply": "Apply", "Staff": "Staff", "#{name} #{programGroupTypeName} Structure": "#{name} #{programGroupTypeName} Structure", "#{title} name": "#{title} name", "#{title} Code": "#{title} Code", "Capacity": "Capacity", "#{title} Status": "#{title} Status", "Created Successfully": "Created Successfully", "#{group} Status": "#{group} Status", "#{subject} Name": "#{subject} Name", "Search a staff": "Search a staff", "Class navigation is not implemented for Staff and Student Learning Space in LMS module, please use My Space for now": "Class navigation is not implemented for Staff and Student Learning Space in LMS module, please use My Space for now", "Ok": "Ok", "Staff Learning Space": "Staff Learning Space", "Search a student": "Search a student", "Student Learning Space": "Student Learning Space", "Timetable": "Timetable", "#{start} to #{end}": "#{start} to #{end}", "Duration, days": "Duration, days", "Timetable duration": "Timetable duration", "Periods": "Periods", "Schedule Summary": "Schedule Summary", "Scheduling": "Scheduling", "Loading intakes...": "Loading intakes...", "Classes number": "Classes number", "Intakes": "Intakes", "#{name} #{title} tree": "#{name} #{title} tree", "Program": "Program", "Subject": "Subject", "Class": "Class", "Student Group": "Student Group", "Timetable name": "Timetable name", "Programs": "Programs", "Status": "Status", "The #{synonym} hasn’t been scheduled yet or incorrect timetable template is used": "The #{synonym} hasn’t been scheduled yet or incorrect timetable template is used", "Total": "Total", "Single": "Single", "Double": "Double", "Triple": "Triple", "Schedule": "Schedule", "Day #{day}": "Day #{day}", "Location conflict": "Location conflict", "Ath the same time": "Ath the same time", "#{location} is allocated to #{subject} #{class}": "#{location} is allocated to #{subject} #{class}", "Student conflict": "Student conflict", "At the same time": "At the same time", "#{student} is participating in #{subjectLocations}": "#{student} is participating in #{subjectLocations}", "#{students} are participating in #{subjectLocations}": "#{students} are participating in #{subjectLocations}", "Student Conflicts": "Student Conflicts", "Staff conflict": "Staff conflict", "#{staff} is allocated to": "#{staff} is allocated to", "Teacher Conflicts": "Teacher Conflicts", "Remove": "Remove", "#{title} Location map": "#{title} Location map", "Class code": "Class code", "Class name": "Class name", "Class Status": "Class Status", "Location": "Location", "#{subjectName} Schedule": "#{subjectName} Schedule", "Students Enrolled": "Students Enrolled", "Are you sure you want to delete period?": "Are you sure you want to delete period?", "All linked attendances will be removed, proceed?": "All linked attendances will be removed, proceed?", "Yes": "Yes", "The duplicates were denied": "The duplicates were denied", "Times are outside timetable block times, please correct": "Times are outside timetable block times, please correct", "Times are overlapping break time": "Times are overlapping break time", "From": "From", "Time": "Time", "Until": "Until", "Number of #{periods}": "Number of #{periods}", "Edit": "Edit", "Session": "Session", "Date": "Date", "Venue": "Venue", "Add to timetable": "Add to timetable", "#{name} group": "#{name} group", "Are you sure you want to delete periods?": "Are you sure you want to delete periods?", "Remove all periods scheduled from Timetable": "Remove all periods scheduled from Timetable", "Requirement (#{duration} Hours)": "Requirement (#{duration} Hours)", "Scheduled (#{scheduled} Hours)": "Scheduled (#{scheduled} Hours)", "#{sessionLength} hours #{type} per #{per} = #{total} hours": "#{sessionLength} hours #{type} per #{per} = #{total} hours", "#{scheduled} hours #{type} scheduled": "#{scheduled} hours #{type} scheduled", ", #{remaining} remaining": ", #{remaining} remaining", "Error": "Error", "Schedule #{class}, #{subject}": "Schedule #{class}, #{subject}", "Days On the Side": "Days On the Side", "Days On Top": "Days On Top", "Go Back": "Go Back", "Week #{number}": "Week #{number}", "Go Forward": "Go Forward", "LMS Role": "LMS Role", "Hours": "Hours", "Break Hours": "Break Hours", "Student": "Student", "enrollment date": "enrollment date", "enrolled by": "enrolled by", "enrollment status": "enrollment status", "#{group}": "#{group}"}, "MESSAGEPORTAL": {"Mark all as read": "Mark all as read", "General Notifications": "General Notifications"}, "MYPROFILE": {"This operation requires confirmation": "This operation requires confirmation", "Email": "Email", "Mobile": "Mobile", "Continue": "Continue", "#{name} Confirmation Required": "#{name} Confirmation Required", "#{name} Confirmation Token": "#{name} Confirmation Token", "Current Timezone": "Current Timezone", "Error": "Error", "You have already unlinked the account. Please try reloading the page to see the changes.": "You have already unlinked the account. Please try reloading the page to see the changes.", "You have not linked your #{name} account to your Edana": "You have not linked your #{name} account to your <PERSON><PERSON>", "Link #{name} Account": "Link #{name} Account", "You have linked your #{name} account on #{date}": "You have linked your #{name} account on #{date}", "Unlink": "Unlink", "Current password expires on #{date} - #{years} left": "Current password expires on #{date} - #{years} left", "Current Password": "Current Password", "New Password": "New Password", "Confirm Password": "Confirm Password"}, "MYSPACE": {"Group Chat": "Group Chat", "Error": "Error", "There is no group chat for this class. Would you like to create one?": "There is no group chat for this class. Would you like to create one?", "Cancel": "Cancel", "Yes": "Yes", "My Learning Space (Staff)": "My Learning Space (Staff)", "#{assessmentTaskCount} assessment #{task}": "#{assessmentTaskCount} assessment #{task}", "#{assessmentTaskWithResultCount} with result": "#{assessmentTaskWithResultCount} with result", "#{calculatedColumnsCount} calculated #{column}": "#{calculatedColumnsCount} calculated #{column}", "Open": "Open", "Locked": "Locked", "Close": "Close", "#{studentsCount} #{students}": "#{studentsCount} #{students}", "Setup": "Setup", "Attendance": "Attendance", "Subject is not scheduled yet. Notify Course Administrator to fix this": "Subject is not scheduled yet. Notify Course Administrator to fix this", "Save": "Save", "Comment": "Comment", "Students": "Students", "Presence Percentage": "Presence Percentage", "Set up attendance codes to view statistics": "Set up attendance codes to view statistics", "Add Comment": "Add Comment", "Attendance Comment": "Attendance Comment", "Session": "Session", "Date": "Date", "Time": "Time", "Absent": "Absent", "Present": "Present", "Late": "Late", "No Attendance Code": "No Attendance Code", "Session Comment": "Session Comment", "Add #{task}": "Add #{task}", "Updated Successfully": "Updated Successfully", "Add Category": "Add Category", "Copy": "Copy", "Delete": "Delete", "Add task": "Add task", "To add tasks, you should select at least one task type on LMS tab of #{activityName}": "To add tasks, you should select at least one task type on LMS tab of #{activityName}", "Add Learning Plan": "Add Learning Plan", "Learning Plan": "Learning Plan", "#{programName} #{subjectName} #{code} learning plan tree": "#{programName} #{subjectName} #{code} learning plan tree", "Task": "Task", "Select Learning Plan": "Select Learning Plan", "Create Learning Plan": "Create Learning Plan", "Learning Plan Name": "Learning Plan Name", "Status": "Status", "Select": "Select", "Out of range": "Out of range", "Markbook": "Markbook", "This subject doesn": "This subject doesn", "Calculation method: Grade": "Calculation method: Grade", "#{rounding} #{decimal}": "#{rounding} #{decimal}", "#{assessedCount} #{name}": "#{assessedCount} #{name}", "#{notAssessedCount} no #{name}": "#{notAssessedCount} no #{name}", "Calculation method: Percentage": "Calculation method: Percentage", "Weight #{weight}": "Weight #{weight}", "Average #{averageMark}": "Average #{averageMark}", "Max #{maxMark}": "Max #{maxMark}", "Min #{minMark}": "Min #{minMark}", "#{zeroMarks} Zero #{result}": "#{zeroMarks} Zero #{result}", "No Zero Result": "No Zero Result", "Out of #{maximum}": "Out of #{maximum}", "Available #{date}": "Available #{date}", "Due #{date}": "Due #{date}", "#{assessedCount} #{value}": "#{assessedCount} #{value}", "#{notAssessedCount} no #{value}": "#{notAssessedCount} no #{value}", "#{zeroMarks} with zero mark": "#{zeroMarks} with zero mark", "#{name} #{count}": "#{name} #{count}", "Submissions": "Submissions", "#{label} (#{fullName})": "#{label} (#{fullName})", "Rounding": "Rounding", "Maximum": "Maximum", "Weight": "Weight", "Rubric Assessment": "Rubric Assessment", "#{currPage} of #{maxPage}": "#{currPage} of #{maxPage}", "Added on #{date} by #{userName}": "Added on #{date} by #{userName}", "Updated on #{date} by #{userName}": "Updated on #{date} by #{userName}", "Add": "Add", "This tasks is now closed for messaging": "This tasks is now closed for messaging", "Response": "Response", "Attachments": "Attachments", "Submitted Audio": "Submitted Audio", "Submitted Video": "Submitted Video", "No submissions yet": "No submissions yet", "Submission": "Submission", "Assessment": "Assessment", "Rubric": "<PERSON><PERSON><PERSON>", "Task Detail": "Task Detail", "Chat": "Cha<PERSON>", "Due By": "Due By", "Task Description": "Task Description", "None": "None", "Attach File": "Attach File", "My Learning Space (Student)": "My Learning Space (Student)", "+ #{num} more": "+ #{num} more", "Calendar": "Calendar", "Search": "Search", "Start Date": "Start Date", "Due Date": "Due Date", "Submission Date": "Submission Date", "Any Time": "Any Time", "Schedule": "Schedule", "+ #{count} more": "+ #{count} more", "Clear": "Clear", "There is no data matching your criteria": "There is no data matching your criteria", "Classes": "Classes", "Result (#{label})": "Result (#{label})", "You have only once opportunity for submission, ": "You have only once opportunity for submission, ", "Proceed with submission?": "Proceed with submission?", "Submit": "Submit", "Submission was permitted until #{date}": "Submission was permitted until #{date}", "Multiple submissions allowed": "Multiple submissions allowed", "Only one submission allowed": "Only one submission allowed", "Record Audio": "Record Audio", "Record Video": "Record Video", "Attachment": "Attachment", "Task List": "Task List", "Loading tasks...": "Loading tasks...", "Today": "Today", "Featured News": "Featured News", "News": "News", "Type": "Type", "Student Name": "Student Name", "First Name": "First Name", "Last Name": "Last Name", "Middle Name": "Middle Name", "Preferred Name": "Preferred Name", "Marks Highlight": "Marks Highlight", "Mark Above": "<PERSON>", "Mark Below": "<PERSON>", "Access": "Access", "Lock Markbook": "Lock Markbook", "Apply to All My #{classPlural}": "Apply to All My #{classPlural}", "Event Type": "Event Type", "Learning Plan Status": "Learning Plan Status", "Task Status": "Task Status", "#{count} #{name} submitted": "#{count} #{name} submitted", "#{count} #{name} not submitted": "#{count} #{name} not submitted", "#{count} #{name} assessed": "#{count} #{name} assessed", "#{count} #{name} not assessed": "#{count} #{name} not assessed", "View": "View"}, "ORGANIZATION": {"Details": "Details", "Initiators": "Initiators", "Assignees": "Assignees", "Workflows": "Workflows", "Last updated on": "Last updated on", "Criteria": "Criteria", "Preview": "Preview", "Person Entity": "Person Entity", "Loading Preview...": "Loading Preview...", "Group by": "Group by", "Loading intakes...": "Loading intakes...", "Classes number": "Classes number", "Intakes": "Intakes", "Add Assignee": "Add Assignee", "Applet Name": "Applet Name", "Applet Code": "Applet Code", "Launchpad Label": "Launchpad Label", "Icon": "Icon", "Description": "Description", "Add Initiator": "Add Initiator", "Add Workflow": "Add Workflow", "#{status}#{isDefault}": "#{status}#{isDefault}", "(Default)": "(<PERSON><PERSON><PERSON>)", "Set as Default": "Set as <PERSON><PERSON><PERSON>", "Remove as Default": "<PERSON><PERSON><PERSON> as <PERSON><PERSON><PERSON>", "Applet Group Name": "Applet Group Name", "Compass Applets Tree": "Compass Applets Tree", "Add Business Process (#{businessAreaName})": "Add Business Process (#{businessAreaName})", "Business Process Name": "Business Process Name", "Organisation Structure": "Organisation Structure", "Applets": "Applets", "#{count} Compass Applets": "#{count} Compass Applets", "No Compass Applets": "No Compass Applets", "by": "by", "Compass Applets": "Compass Applets", "#{count} Business #{process}": "#{count} Business #{process}", "Loading...": "Loading...", "Loading workflow...": "Loading workflow...", "Design": "Design", "Business Area": "Business Area", "Status": "Status", "Parent Group": "Parent Group", "Loading types...": "Loading types...", "Ticket Type": "Ticket Type", "Libraries": "Libraries", "#{count} #{library}": "#{count} #{library}", "Resources": "Resources", "Role should be unique": "Role should be unique", "Library Name": "Library Name", "Process Management": "Process Management", "Added role(s) will be removed, proceed?": "Added role(s) will be removed, proceed?", "Remove ": "Remove ", "Are you sure you want to remove this role?": "Are you sure you want to remove this role?", "Remove": "Remove", "Cancel": "Cancel", "Role": "Role", "Folder name": "Folder name", "Sequence": "Sequence", "Graphic Image": "Graphic Image", "Search": "Search", "Copy": "Copy", "Error": "Error", "Create": "Create", "Update": "Update", "Resource name": "Resource name", "Content Heading Label": "Content Heading Label", "Content Subheading Label": "Content Subheading Label", "Framework": "Framework", "Topic Keyword": "Topic Keyword", "Content Keyword": "Content Keyword", "Content Category": "Content Category", "Source": "Source", "Author": "Author", "Content": "Content", "Role Assignment": "Role Assignment", "Audio Files": "Audio Files", "Number of Audio Files": "Number of Audio Files", "From": "From", "To": "To", "Maximum Size of Each Audio File": "Maximum Size of Each Audio File", "Audio Caption": "Audio Caption", "Audio Validation": "Audio Validation", "Documents": "Documents", "Number of Documents": "Number of Documents", "Maximum Size of Each Document": "Maximum Size of Each Document", "Document Caption ": "Document Caption ", "Images": "Images", "Number of Images": "Number of Images", "Maximum Size of Each Image": "Maximum Size of Each Image", "Image Caption": "Image Caption", "Image AI Improvement": "Image AI Improvement", "Questions": "Questions", "Number of Questions": "Number of Questions", "Enter Category": "Enter Category", "Maximum Characters of": "Maximum Characters of", "Texts": "Texts", "Number of Text boxes": "Number of Text boxes", "Height (Lines)": "Height (Lines)", "Maximum Characters": "Maximum Characters", "Preview Rendering": "Preview Rendering", "URLs": "URLs", "Number of URLs": "Number of URLs", "URL Caption": "URL Caption", "Video Files": "Video Files", "Number of Video Files": "Number of Video Files", "Maximum Size of Each Video File": "Maximum Size of Each Video File", "Video Caption": "Video Caption", "Ed-com Setup": "Ed-com Setup", "Email Categories": "Email Categories", "Email Gateways": "Email Gateways", "Email Channel": "Email Channel", "Category Name": "Category Name", "Folder Name": "Folder Name", "#{name} Email Categories": "#{name} Email Categories", "#{count} #{category}": "#{count} #{category}", "Domain Address": "Domain Address", "Global Sender Name": "Global Sender Name", "Global Sender Email": "Global Sender Email", "Priority": "Priority", "It works": "It works", "Unable to connect, check your settings and try again": "Unable to connect, check your settings and try again", "Verify Gateway": "Verify Gateway", "Verified by #{firstName} #{lastName} on #{date}": "Verified by #{firstName} #{lastName} on #{date}", "Go Back": "Go Back", "Name": "Name", "Gateway Address": "Gateway Address", "Maximum File Size": "Maximum File Size", "Port Number": "Port Number", "User Name": "User Name", "Password": "Password", "Domain": "Domain", "#{count} #{gateway}": "#{count} #{gateway}", "News Categories": "News Categories", "News Channel": "News Channel", "#{name} News Categories": "#{name} News Categories", "Notification Categories": "Notification Categories", "Notification Channel": "Notification Channel", "Updated Successfully": "Updated Successfully", "#{name} Notification": "#{name} Notification", "Add #{name}": "Add #{name}", "Delete": "Delete", "Add #{typeName}": "Add #{typeName}", "#{name} Name": "#{name} Name", "Asset Category Name": "Asset Category Name", "Utilization": "Utilization", "Asset Folder Name": "Asset Folder Name", "Asset Group Name": "Asset Group Name", "Asset Groups": "Asset Groups", "#{count} #{group}": "#{count} #{group}", "Open": "Open", "Facility Management": "Facility Management", "Loading groups...": "Loading groups...", "This location already has #{assetsCount} assets allocated. Move those assets out of this location before unticking": "This location already has #{assetsCount} assets allocated. Move those assets out of this location before unticking", "Location Name": "Location Name", "Location Type": "Location Type", "This is an asset": "This is an asset", "Asset Code": "Asset Code", "Asset Group": "Asset Group", "Asset Category": "Asset Category", "It contains other assets": "It contains other assets", "Location will be unlinked from asset. Are you sure ?": "Location will be unlinked from asset. Are you sure ?", "Ok": "Ok", "#{title} Location map": "#{title} Location map", "Location Map": "Location Map", "Search for asset": "Search for asset", "Import Person Database": "Import Person Database", "Template": "Template", "Import": "Import", "Loading last import session": "Loading last import session", "Enter valid data first": "Enter valid data first", "Please ensure your first row of import file contain headings for each column": "Please ensure your first row of import file contain headings for each column", "Drop CSV or XLSX file here": "Drop CSV or XLSX file here", "Uploaded By": "Uploaded By", "Uploaded At": "Uploaded At", "Canceling the import process will remove all the data and you should start the process from scratch": "Canceling the import process will remove all the data and you should start the process from scratch", "Legacy Unique Id": "Legacy Unique Id", "Person Entity Type": "Person Entity Type", "Title": "Title", "First Name": "First Name", "Middle Name": "Middle Name", "Last Name": "Last Name", "Preferred Name": "Preferred Name", "Salutation": "Salutation", "Gender": "Gender", "Date of Birth": "Date of Birth", "Country of Birth": "Country of Birth", "Marital Status": "Marital Status", "Organisation Level": "Organisation Level", "Valid From": "<PERSON><PERSON>", "Valid To": "<PERSON><PERSON>", "ID Number": "ID Number", "ID Valid From": "ID Valid From", "ID Valid To": "ID Valid To", "DB": "DB", "All Records (#{count})": "All Records (#{count})", "Mandatory Fields Validation (#{count})": "Mandatory Fields Validation (#{count})", "Field Value Validation (#{count})": "Field Value Validation (#{count})", "Import Duplicate Check (#{count})": "Import Duplicate Check (#{count})", "Database Duplicate Check (#{count})": "Database Duplicate Check (#{count})", "Excluded (#{count})": "Excluded (#{count})", "Importing ": "Importing ", "Loading Preview": "Loading Preview", "Total Records": "Total Records", "The template file contains these columns, bold items are mandatory.": "The template file contains these columns, bold items are mandatory.", "Please ensure the mapping on the first row is correct.": "Please ensure the mapping on the first row is correct.", "Loading Import Rules...": "Loading Import Rules...", "Bold items in the template table are mandatory; Keeping these empty will cause errors": "Bold items in the template table are mandatory; Keeping these empty will cause errors", "Legacy Unique ID": "Legacy Unique ID", "It is necessary for the subsequent import and the max number of characters allowed for this field is #{number}": "It is necessary for the subsequent import and the max number of characters allowed for this field is #{number}", "#{entities} are the only available person entity types": "#{entities} are the only available person entity types", "#{entities} are the only available titles": "#{entities} are the only available titles", "The max number of characters allowed for this field is #{number}": "The max number of characters allowed for this field is #{number}", "Surname": "Surname", "#{entities} are only available genders": "#{entities} are only available genders", "It should be only for today or the past. Also, the inserted date should follow only this format: #{date}": "It should be only for today or the past. Also, the inserted date should follow only this format: #{date}", "#{entities} are only available marital statuses": "#{entities} are only available marital statuses", "#{entities} are only available statuses": "#{entities} are only available statuses", "This is related to the Role. The inserted date should follow only this format: #{date}": "This is related to the Role. The inserted date should follow only this format: #{date}", "This is related to the Role. The inserted date should follow only this format: #{date}. Also, the date can\\": "This is related to the Role. The inserted date should follow only this format: #{date}. Also, the date can\\", "This is related to the ID Number. The inserted date should follow only this format: #{date}": "This is related to the ID Number. The inserted date should follow only this format: #{date}", "This is related to the ID Number. The inserted date should follow only this format: #{date}. Also, the date can’t be before the inserted date in the “#{field}” field": "This is related to the ID Number. The inserted date should follow only this format: #{date}. Also, the date can’t be before the inserted date in the “#{field}” field", "Close": "Close", "The template file contains these columns, you can check": "The template file contains these columns, you can check", "here": "here", "to see the rules for each column": "to see the rules for each column", "Download Above Template XLSX File": "Download Above Template XLSX File", "Rules For Importing Data": "Rules For Importing Data", "Click": "Click", "to see the list of available countries": "to see the list of available countries", "Available Countries": "Available Countries", "The identity rule hasn’t been specified.": "The identity rule hasn’t been specified.", "Loading Identity Rules...": "Loading Identity Rules...", "Only one ID number can be added for the import, be careful to use the one related to the added role. The max number of characters allowed for this field is 15. Check": "Only one ID number can be added for the import, be careful to use the one related to the added role. The max number of characters allowed for this field is 15. Check", "for identity rules related to each person entity types. You can still import the data without following the identity rule": "for identity rules related to each person entity types. You can still import the data without following the identity rule", "Identity Rules": "Identity Rules", "Login and Security": "Login and Security", "Minimum Password Complexity: #{complexity}": "Minimum Password Complexity: #{complexity}", "Login Retries Limit: #{limit}": "Login Retries Limit: #{limit}", "Security Profile Name": "Security Profile Name", "Login Retries Limit": "Login Retries Limit", "Minimum Password length": "Minimum Password length", "Contains at least one number": "Contains at least one number", "Contains at least Uppercase alphabet characters (A–Z) and Lowercase alphabet characters (a–z)": "Contains at least Uppercase alphabet characters (A–Z) and Lowercase alphabet characters (a–z)", "Contains Non Alphanumeric characters (for example, !$#,%)": "Contains Non Alphanumeric characters (for example, !$#,%)", "Minimum Password Complexity": "Minimum Password Complexity", "Indefinite": "Indefinite", "Password Expiration Days": "Password Expiration Days", "Security Profiles": "Security Profiles", "#{count} Security #{profile}": "#{count} Security #{profile}", "#{count} #{versionLabel}": "#{count} #{versionLabel}", "Default Version #{version}": "Default Version #{version}", "Last updated by #{name}": "Last updated by #{name}", "#{count} Terms & Conditions": "#{count} Terms & Conditions", "Terms & Conditions Versions": "Terms & Conditions Versions", "User Agreements": "User Agreements", "Terms & Conditions": "Terms & Conditions", "User Friendly Name": "User Friendly Name", "Version #{version}": "Version #{version}", "Created by #{name}": "Created by #{name}", "#{agreementLabel} #{count}": "#{agreement<PERSON>abel} #{count}", "Terms and Conditions Version": "Terms and Conditions Version", "Version Name": "Version Name", "Add Terms & Conditions Version": "Add Terms & Conditions Version", "generated on #{date}, by #{person}": "generated on #{date}, by #{person}", "Version": "Version", "Active From": "Active From", "PDF Version": "PDF Version", "Add New Version": "Add New Version", "Loading Versions...": "Loading Versions...", "Add Organisation Group": "Add Organisation Group", "Organisation Group Name": "Organisation Group Name", "#{count} Holiday #{calendar}": "#{count} Holiday #{calendar}", "Date": "Date", "Staff": "Staff", "Students": "Students", "Type": "Type", "Holiday": "Holiday", "Holiday Calendar Name": "Holiday Calendar Name", "Countries specific holiday calendar": "Countries specific holiday calendar", "Religious Calendars": "Religious Calendars", "Country": "Country", "N/A": "N/A", "Organisation Structure Name": "Organisation Structure Name", "Roles": "Roles", "Groups": "Groups", "Group name": "Group name", "Group Roles": "Group Roles", "Role Name": "Role Name", "#{criteriaSetName} has already been set up in #{groupName}. Closing the tab will remove the custom criteria": "#{criteriaSetName} has already been set up in #{groupName}. Closing the tab will remove the custom criteria", "Program Group": "Program Group", "Add Custom Criteria": "Add Custom Criteria", "Add Person": "Add Person", "Criteria has already been set up in #{name}. \\n Changing the group membership rule will remove the criteria?": "Criteria has already been set up in #{name}. \\n Changing the group membership rule will remove the criteria?", "Group Members": "Group Members", "Please set up at least one BE using Organisation Structure before setting up groups": "Please set up at least one BE using Organisation Structure before setting up groups", "Add Group Members": "Add Group Members", "Persons": "Persons", "#{years}y": "#{years}y", "#{months}m": "#{months}m", "#{start} and #{end}": "#{start} and #{end}", "Loading Cities...": "Loading Cities...", "Loading Countries...": "Loading Countries...", "Delete #{name}?": "Delete #{name}?", "Are you sure you want to delete the Filter?": "Are you sure you want to delete the Filter?", "Filters": "Filters", "Query Design": "Query Design", "Loading Nationalities...": "Loading Nationalities...", "Loading Person Entity Types...": "Loading Person Entity Types...", "Clear": "Clear", "Organisations": "Organisations", "Loading Organisations...": "Loading Organisations...", "Specify Criteria": "Specify Criteria", "More than Start Year": "More than Start Year", "Nationality": "Nationality", "Year": "Year", "Month": "Month", "And": "And", "Address Types": "Address Types", "State": "State", "City": "City", "Criteria is not supported": "Criteria is not supported", "Loading Programs...": "Loading Programs...", "Programs": "Programs", "Loading Program Groups...": "Loading Program Groups...", "Loading Roles...": "Loading Roles...", "Loading Subjects...": "Loading Subjects...", "Subjects": "Subjects", "#{criteria} criteria weren": "#{criteria} criteria weren", "#{name} person roles": "#{name} person roles", "Add Role Group": "Add Role Group", "Add Role": "Add Role", "Role Group Name": "Role Group Name", "Permissions": "Permissions", "Save": "Save", "Attach Role": "Attach <PERSON>", "Add Level": "Add Level", "Level Name": "Level Name", "Parent Level": "Parent Level", "Organisation structure": "Organisation structure", "#{count} #{structure}": "#{count} #{structure}", "Organisation tree": "Organisation tree", "#{count} Business #{entity}": "#{count} Business #{entity}", "Add #{businessEntity}": "Add #{businessEntity}", "#{businessEntity} Name": "#{businessEntity} Name", "Default Language": "Default Language", "Default Timezone": "Default Timezone", "Branding": "Branding", "Small Logo": "Small Logo", "Some reports or functions in the system need to use Small Logo only. For example:": "Some reports or functions in the system need to use Small Logo only. For example:", "Medium Logo": "Medium Logo", "This is used when the logo needs to stand out like on a cover page of a report. For example:": "This is used when the logo needs to stand out like on a cover page of a report. For example:", "Large Logo": "Large Logo", "This is used when the logo needs to stand out like on a cover page of a report, same as Medium Logo, but bigger. For example:": "This is used when the logo needs to stand out like on a cover page of a report, same as Medium Logo, but bigger. For example:", "Wide Logo": "Wide Logo", "This is used to cover the top of the page. For example:": "This is used to cover the top of the page. For example:", "#{name} Organisation Tree": "#{name} Organisation Tree", "General": "General", "Detail": "Detail", "Email Signature": "Email Signature", "Attendance Codes": "Attendance Codes", "Work Schedules": "Work Schedules", "Contract Types": "Contract Types", "Location": "Location", "Last Updated": "Last Updated", "Default": "<PERSON><PERSON><PERSON>", "Add Contract Type": "Add Contract Type", "Contract Type Name": "Contract Type Name", "Person Information Tabs": "Person Information Tabs", "Email signature name": "Email signature name", "Identity Rule Name": "Identity Rule Name", "Field Label": "Field Label", "Rule": "Rule", "Input Mask": "Input Mask", "Enter user hint": "Enter user hint", "Valid From Field": "<PERSON><PERSON>", "Valid To Field": "<PERSON><PERSON>", "Enable Location in Person Entity Location": "Enable Location in Person Entity Location", "Location Use": "Location Use", "Code": "Code", "Add Staff Attendance Code Set": "Add Staff Attendance Code Set", "#{count} attendance #{code}": "#{count} attendance #{code}", "Attendance Defaults": "Attendance Defaults", "Default Present Code": "Default Present Code", "Default Absent Code": "Default Absent Code", "Default Late Code": "Default Late Code", "Default attendance codes should be selected to update the Attendance Code Set with Active status. Click on Confirm to update the Attendance Code Set with Inactive status or Cancel to edit": "Default attendance codes should be selected to update the Attendance Code Set with Active status. Click on Confirm to update the Attendance Code Set with Inactive status or Cancel to edit", "Confirm": "Confirm", "Add Attendance Code": "Add Attendance Code", "Attendance Code Set Name": "Attendance Code Set Name", "You can": "You can", "Add Work Schedule Day": "Add Work Schedule Day", "Work Hours": "Work Hours", "Check-in Time": "Check-in Time", "Check-out Time": "Check-out Time", "Break Time": "Break Time", "Add Work Schedule": "Add Work Schedule", "#{count} Work #{day}": "#{count} Work #{day}", "Work Schedule Name": "Work Schedule Name", "#{count} #{record}": "#{count} #{record}", "#{pet} person entity could not be set as inactive ": "#{pet} person entity could not be set as inactive ", "Core Person Entities (#{count})": "Core Person Entities (#{count})", "Non Core Person Entities (#{count})": "Non Core Person Entities (#{count})", "Inactive Person Entities (#{count})": "Inactive Person Entities (#{count})", "Tabs": "Tabs", "Quest": "Quest", "Surveys": "Surveys", "Categories": "Categories", "Templates": "Templates", "Add Category": "Add Category", "Should be unique": "Should be unique", "Survey Template Name": "Survey Template Name", "Page Header": "<PERSON> Header", "Page Footer": "<PERSON>", "Design Image": "Design Image", "Organisation Groups": "Organisation Groups"}, "PEOPLERECORDS": {"Loading person info": "Loading person info", "Add Person": "Add Person", "Loading person": "Loading person", "Invitation sent to #{fullName} (#{email})": "Invitation sent to #{fullName} (#{email})", "Error": "Error", "Invite Person": "Invite <PERSON>", "Relations is not Indicated": "Relations is not Indicated", "Loading...": "Loading...", "No Relatives": "No Relatives", "Relations": "Relations", "This address is shared with #{count} #{entity}, by deleting this address it will be deleted from their record. Are you sure you want to delete this address permanently?": "This address is shared with #{count} #{entity}, by deleting this address it will be deleted from their record. Are you sure you want to delete this address permanently?", "Cancel": "Cancel", "Yes": "Yes", "Save": "Save", "Address": "Address", "Preview": "Preview", "Send #{name}": "Send #{name}", "Application Notification": "Application Notification", "Add #{name}": "Add #{name}", "N/A": "N/A", "Edit #{name}": "Edit #{name}", "Send": "Send", "#{type} Sent Successfully": "#{type} Sent Successfully", "Subject": "Subject", "Email Content": "Email Content", "Cc": "Cc", "Bcc": "Bcc", "Cc to me": "Cc to me", "Bcc to me": "Bcc to me", "Details": "Details", "Notification Content": "Notification Content", "Communication Setup": "Communication Setup", "Email Signature": "Email Signature", "Default Organisation Signature": "Default Organisation Signature", "Email Signature Templates": "Email Signature Templates", "Email signature template(s) will be removed, proceed?": "Email signature template(s) will be removed, proceed?", "Custom Email Signature": "Custom Email Signature", "Custom email signature(s) will be removed, proceed?": "Custom email signature(s) will be removed, proceed?", "Physical Signature": "Physical Signature", "Physical signature(s) will be removed, proceed?": "Physical signature(s) will be removed, proceed?", "Add signature": "Add signature", "more": "more", "Signature Name": "Signature Name", "Add Email Signature Template": "Add Email Signature Template", "This attachment already exists, replace or add the new file": "This attachment already exists, replace or add the new file", "New file": "New file", "Replace": "Replace", "No less than #{minimumNumberOfFiles} files allowed": "No less than #{minimumNumberOfFiles} files allowed", "No more than #{allowedNumberOfFiles} files allowed": "No more than #{allowedNumberOfFiles} files allowed", "Set as Default": "Set as <PERSON><PERSON><PERSON>", "Remove": "Remove", "Default": "<PERSON><PERSON><PERSON>", "Caption ": "Caption ", "(Default)": "(<PERSON><PERSON><PERSON>)", "Add Contract": "Add Contract", "Check In Rule": "Check In Rule", "Enable Check In": "Enable Check In", "Photo Rule": "Photo Rule", "Enable Check Out": "Enable Check Out", "Position": "Position", "Contract Start Date": "Contract Start Date", "Contract End Date": "Contract End Date", "Work Schedule": "Work Schedule", "Attendance Code Set": "Attendance Code Set", "Enter status notes": "Enter status notes", "Add Education and Training": "Add Education and Training", "#{count} similar #{record} found": "#{count} similar #{record} found", "Please select one or click Close to continue saving the new record": "Please select one or click Close to continue saving the new record", "Search for Institution": "Search for Institution", "Institution Name": "Institution Name", "Country": "Country", "City": "City", "Phone": "Phone", "Email": "Email", "From Year": "From Year", "From Month": "From Month", "To Year": "To Year", "From To": "From To", "Education and Training Notes": "Education and Training Notes", "Emergency Contacts": "Emergency Contacts", "Role": "Role", "Valid From": "<PERSON><PERSON>", "Valid To": "<PERSON><PERSON>", "Reporting To": "Reporting To", "Ok": "Ok", "Person session could be expired due to Person Entity allocation changes": "Person session could be expired due to Person Entity allocation changes", "Confirm": "Confirm", "Entity Location": "Entity Location", "Loading Person Entity Allocations": "Loading Person Entity Allocations", "Create Personal Information": "Create Personal Information", "Personal Information": "Personal Information", "Cancel Update": "Cancel Update", "I will finish this later": "I will finish this later", "Save and Goto Next": "Save and Goto Next", "I confirm all details are correct": "I confirm all details are correct", "You can not set your own status to #{status}": "You can not set your own status to #{status}", "Required": "Required", "First Name": "First Name", "Last Name": "Last Name", "Middle Name": "Middle Name", "Preferred Name": "Preferred Name", "Salutation": "Salutation", "Gender": "Gender", "Date of Birth": "Date of Birth", "Place of Birth": "Place of Birth", "#{count} #{maybePlural} found in the Company Database": "#{count} #{maybePlural} found in the Company Database", "Companies with this address": "Companies with this address", "Company Name": "Company Name", "Relation with #{personName}": "Relation with #{personName}", "Admin relation with #{personName}": "Admin relation with #{personName}", "Comment": "Comment", "Save and Enter Another": "Save and Enter Another", "Save and Enter Another at the Same Address": "Save and Enter Another at the Same Address", "Contact": "Contact", "Are you sure you want to delete #{entity} with #{itemName} ?": "Are you sure you want to delete #{entity} with #{itemName} ?", "Delete": "Delete", "Edit": "Edit", "Go to Person Record": "Go to Person Record", "Add #{relation} is disabled because in #{orgGroupName} organization group #{pet} person entity is disabled": "Add #{relation} is disabled because in #{orgGroupName} organization group #{pet} person entity is disabled", "Add Emergency Contact": "Add Emergency Contact", "Add Person Relation": "Add Person Relation", "Add Company Relation": "Add Company Relation", "searching...": "searching...", "Search for Company": "Search for Company", "#{count} similar record#{s} found": "#{count} similar record#{s} found", "#{count} #{maybePlural} found in the Person Database": "#{count} #{maybePlural} found in the Person Database", "Please select one or click cancel to continue saving the new record": "Please select one or click cancel to continue saving the new record", "Surname": "Surname", "Are you sure you want to delete this #{name}?": "Are you sure you want to delete this #{name}?", "work position": "work position", "Update": "Update", "Clear": "Clear", "history work position": "history work position", "Add History Work": "Add History Work", "History Work": "History Work", "Work": "Work", "New Person": "New Person", "Next": "Next", "Open": "Open", "Expired #{date}": "Expired #{date}", "Valid to #{date}": "Valid to #{date}", "Role Status": "Role Status", "Person": "Person", "Person Database": "Person Database", "Location": "Location"}, "PROGRAMS": {"Add Program Calendar": "Add Program Calendar", "Program Calendar Name": "Program Calendar Name", "Number of Cycle in a Year": "Number of Cycle in a Year", "Status": "Status", "Can’t create another #{cycle}. #{cycle}s quantity should be equal to the number of cycles in a year": "Can’t create another #{cycle}. #{cycle}s quantity should be equal to the number of cycles in a year", "Add #{cycle}": "Add #{cycle}", "#{cycle} Name": "#{cycle} Name", "#{cycle} Duration": "#{cycle} Duration", "#{programCalendarCount} #{calendar}": "#{programCalendarCount} #{calendar}", "Updated Successfully": "Updated Successfully", "Add Program Year": "Add Program Year", "Add #{name}": "Add #{name}", "Add Yearly Planner ": "Add Yearly Planner ", "Delete": "Delete", "Search": "Search", "Program Calendar Cycles": "Program Calendar Cycles", "Program Cycle Type": "Program Cycle Type", "Program Year Name": "Program Year Name", "Program Year Duration": "Program Year Duration", "Add Yearly Planner": "Add Yearly Planner", "Yearly Planner Name": "Yearly Planner Name", "Number of #{type}": "Number of #{type}", "Add": "Add", "Open": "Open", "Remove": "Remove", "Add #{title}": "Add #{title}", "Number": "Number", "Period Abbreviation": "Period Abbreviation", "Period Duration": "Period Duration", "N/A": "N/A", "Catalog": "Catalog", "Structure": "Structure", "Add Program Group #{programGroupType}": "Add Program Group #{programGroupType}", "Learning Plans": "Learning Plans", "Assessment": "Assessment", "Curriculum Structure": "Curriculum Structure", "Level 1": "Level 1", "Level 2": "Level 2", "#{count} #{name}": "#{count} #{name}", "#{count} #{entityLabel}#{s}": "#{count} #{entity<PERSON>abel}#{s}", "#{subLabel}": "#{subLabel}", "#{count} attendance code#{s}": "#{count} attendance code#{s}", "Last update #{date}": "Last update #{date}", "Updated by #{user}": "Updated by #{user}", "Attendance Code Set Name": "Attendance Code Set Name", "Add Attendance Code Set": "Add Attendance Code Set", "Attendance Code Sets": "Attendance Code Sets", "Created Successfully": "Created Successfully", "Add Attendance Code": "Add Attendance Code", "Attendance Codes": "Attendance Codes", "Symbol": "Symbol", "Type": "Type", "Edit Access Level": "Edit Access Level", "Calculation": "Calculation", "Icon": "Icon", "Cancel": "Cancel", "Go Back": "Go Back", "#{count} #{label}": "#{count} #{label}", "#{count} #{entity}": "#{count} #{entity}", "Details": "Details", "Process": "Process", "Assessment Criteria": "Assessment Criteria", "Description": "Description", "Criteria Group Name": "Criteria Group Name", "Program Assessment Criteria": "Program Assessment Criteria", "#{programName} Assessment Criteria": "#{programName} Assessment Criteria", "#{programName} Assessment Criteria Name": "#{programName} Assessment Criteria Name", "#{programName} Comment Banks": "#{programName} Comment Banks", "#{title} Comment Bank Name": "#{title} Comment Bank Name", "Comment": "Comment", "Code": "Code", "#{programName} Comments": "#{programName} Comments", "Comment Bank": "Comment Bank", "#{programName} Comment Name": "#{programName} Comment Name", "From": "From", "Characters": "Characters", "To": "To", "Use #{name} comment banks": "Use #{name} comment banks", "#{programName}": "#{programName}", "Maximum value can’t exceed #{MAX_VALUE}": "Maximum value can’t exceed #{MAX_VALUE}", "Remove #{name}?": "Remove #{name}?", "Are you sure you want to remove #{assessmentBoundaryTitle} ?": "Are you sure you want to remove #{assessmentBoundaryTitle} ?", "Removed Successfully": "Removed Successfully", "Add Comment": "Add Comment", "Error": "Error", "No more than #{MAX_AMOUNT_OF_GRADES} #{assessmentBoundaryTitlePlural} is allowed": "No more than #{MAX_AMOUNT_OF_GRADES} #{assessmentBoundaryTitlePlural} is allowed", "#{program} Description": "#{program} Description", "#{name} Name": "#{name} Name", "Rounding": "Rounding", "#{programName} Option Set Name": "#{programName} Option Set Name", "Option": "Option", "Value": "Value", "Options": "Options", "Add #{programName} Option Set": "Add #{programName} Option Set", "#{programName} Option Sets": "#{programName} Option Sets", "Subject Assessment Criteria": "Subject Assessment Criteria", "#{activityName} Assessment Criteria": "#{activityName} Assessment Criteria", "Data Type": "Data Type", "Decimals": "Decimals", "Option Set": "Option Set", "Range": "Range", "Add #{activityName} Assessment Criteria": "Add #{activityName} Assessment Criteria", "#{activityName} Assessment Criteria Name": "#{activityName} Assessment Criteria Name", "#{title} Comment Bank": "#{title} Comment Bank", "#{activityName} Comment Banks": "#{activityName} Comment Banks", "Comments": "Comments", "#{count} Comment": "#{count} Comment", "#{count} Comments": "#{count} Comments", "#{activityName} Comments": "#{activityName} Comments", "Rule": "Rule", "#{activityName} Comment Name": "#{activityName} Comment Name", "#{activityName}": "#{activityName}", "#{activityName} Option Set Name": "#{activityName} Option Set Name", "Add #{activityName} Option Set": "Add #{activityName} Option Set", "#{activityName} Option Sets": "#{activityName} Option Sets", "Used In": "Used In", "Current View": "Current View", "Full Report": "Full Report", "Download as PDF": "Download as PDF", "#{learningPlansCount} #{name} (#{status})": "#{learningPlansCount} #{name} (#{status})", "#{group} #{name}": "#{group} #{name}", "Add #{task}": "Add #{task}", "Add Category": "Add Category", "Add task": "Add task", "To add tasks, you should select at least one task type on LMS tab of #{activityName}": "To add tasks, you should select at least one task type on LMS tab of #{activityName}", "Number of Tasks": "Number of Tasks", "#{title} Name": "#{title} Name", "Tasks": "Tasks", "#{count} #{task} (#{status})": "#{count} #{task} (#{status})", "0 task": "0 task", "Ok": "Ok", "#{length} resources already exist, you can": "#{length} resources already exist, you can", "Clear": "Clear", "The subject has already been scheduled in the timetable. Changing the data will cancel the planning results.": "The subject has already been scheduled in the timetable. Changing the data will cancel the planning results.", "Add Folder": "Add Folder", "Add #{activityName}": "Add #{activityName}", "#{subjectsCount} #{name}": "#{subjectsCount} #{name}", "Folder Name": "Folder Name", "Add #{programActivityName}": "Add #{programActivityName}", "Colour": "Colour", "Attach File": "Attach File", "Attachment": "Attachment", "LMS": "LMS", "Resources": "Resources", "Schedule": "Schedule", "Markbook": "Markbook", "Attendance": "Attendance", "Attendance code set": "Attendance code set", "Task types used in #{name}": "Task types used in #{name}", "Learning plan": "Learning plan", "Other Options": "Other Options", "Submissions": "Submissions", "Added Successfully": "Added Successfully", "Removing #{name} will unlink all associated items and contents in the learning plan.": "Removing #{name} will unlink all associated items and contents in the learning plan.", "Proceed": "Proceed", "Add Resource": "Add Resource", "No resource selected": "No resource selected", "Session hours and credits": "Session hours and credits", "Timetable": "Timetable", "Credits": "Credits", "Total Periods: #{periods}": "Total Periods: #{periods}", "Program Group Name": "Program Group Name", "Apply": "Apply", "#{entityName} #{programNamePlural} tree": "#{entityName} #{programNamePlural} tree", "Attach #{programName}": "Attach #{programName}", "Are you sure you want to delete #{name} ?": "Are you sure you want to delete #{name} ?", "Activate": "Activate", "Inactivate": "Inactivate", "Organisation Structure": "Organisation Structure", "#{programLocationsCount} #{programName} #{classLocationName}": "#{programLocationsCount} #{programName} #{classLocationName}", "#{activityName} Catalogue": "#{activityName} Catalogue", "Added Succesfully": "Added Succesfully", "#{programStructureSubjectsCount} #{name}": "#{programStructureSubjectsCount} #{name}", "Add Subject": "Add Subject", "#{activityName} Name": "#{activityName} Name", "Subject Rule": "Subject Rule", "#{programsCount} #{name}": "#{programsCount} #{name}", "Default Group Size": "Default Group Size", "Setup program calendar before creating program groups": "Setup program calendar before creating program groups", "Program Groups": "Program Groups", "Create": "Create", "Update": "Update", "Access": "Access", "Advance": "Advance", "Screen Comment Name": "Screen Comment Name", "Report Comment Name": "Report Comment Name", "Image Upload Heading": "Image Upload Heading", "Level #{value}": "Level #{value}", "Approval": "Approval", "Loading Process Tab": "Loading Process Tab", "#{class}": "#{class}", "#{activity}": "#{activity}", "#{program}": "#{program}", "No Limit": "No Limit", "Image Upload": "Image Upload", "Program Holiday Calendars": "Program Holiday Calendars", "#{count} Holiday #{calendar}": "#{count} Holiday #{calendar}", "Date Range": "Date Range", "Holiday Name": "Holiday Name", "For Staff": "For Staff", "For Students": "For Students", "Are you sure you want to remove this holiday?": "Are you sure you want to remove this holiday?", "Add Holiday": "Add Holiday", "Holidays": "Holidays", "Holiday Calendar Name": "Holiday Calendar Name", "Organisation Tree": "Organisation Tree", "Add #{name} #{title} Template": "Add #{name} #{title} Template", "Timetable Templates": "Timetable Templates", "#{days} #{value}": "#{days} #{value}", "#{days} Days": "#{days} Days", "Variable": "Variable", "Timetable Template Name": "Timetable Template Name", "Usual Daily Start and End Time": "Usual Daily Start and End Time", "Usual Number of Periods and Breaks per Day": "Usual Number of Periods and Breaks per Day", "minutes": "minutes", "Usual Length of Each Period": "Usual Length of Each Period", "Week Starts On": "Week Starts On", "Days Of Week": "Days Of Week", "Number of Days in the Cycle": "Number of Days in the Cycle", "Number Of Weeks": "Number Of Weeks", "Week": "Week", "Timetable Grid": "Timetable Grid", "Copy to next row": "Co<PERSON> to next row", "Copy to all rows below": "Copy to all rows below", "Day": "Day", "Edit time": "Edit time", "#{count} #{template}": "#{count} #{template}"}, "SITEMANAGEMENT": {"Branding": "Branding", "Site Name": "Site Name", "Login Page Message": "Login Page Message", "Small Logo": "Small Logo", "This image will be used as the logo of the login screen.": "This image will be used as the logo of the login screen.", "Login Background Image for Web Application": "Login Background Image for Web Application", "This image will be used as the background of the login screen for the web application.": "This image will be used as the background of the login screen for the web application.", "Login Background Image for Mobile App": "Login Background Image for Mobile App", "This Image will be used as the background of the login screen for mobile application.": "This Image will be used as the background of the login screen for mobile application.", "Validating": "Validating", "Validate": "Validate", "Detail": "Detail", "Client name": "Client name", "Country": "Country", "Site Link": "Site Link", "License": "License", "License Key": "License Key", "License Name": "License Name", "Users": "Users", "Go Back": "Go Back", "Add SMS Gateway": "Add SMS Gateway", "Relation Priority": "Relation Priority"}, "SYSTEM": {"AI Image Generation": "AI Image Generation", "#{status} (#{reason})": "#{status} (#{reason})", "N/A": "N/A", "#{status} (#{error}) on #{date}": "#{status} (#{error}) on #{date}", "#{status} on #{date}": "#{status} on #{date}", "Request Status": "Request Status", "Tenant": "Tenant", "Search": "Search", "Storage Management": "Storage Management", "Storage Tree": "Storage Tree", "Loading storage settings...": "Loading storage settings...", "Storage Area": "Storage Area", "must be not more 200 characters": "must be not more 200 characters", "Subfolder": "Subfolder", "Folder": "Folder", "Maximum File Size": "Maximum File Size", "Loading statistics...": "Loading statistics...", "Statistics": "Statistics", "Number of Files": "Number of Files", "Used Quota Space": "Used Quota Space", "Unlimited": "Unlimited", "Free Quota Space": "Free Quota Space", "Text-To-Audio Usage History": "Text-To-Audio Usage History", "Generation Status": "Generation Status", "Status": "Status", "Login Status": "Login Status", "Tenants": "Tenants", "Platform": "Platform", "Validation Status": "Validation Status"}, "TRANSLATION": {"Save": "Save", "Cancel": "Cancel", "Enter Translation": "Enter Translation", "Updated Succesfully": "Updated Succesfully", "Translation": "Translation", "Language": "Language", "Module": "<PERSON><PERSON><PERSON>", "Search": "Search"}}